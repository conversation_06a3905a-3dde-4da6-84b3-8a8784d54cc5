/**
 * 错误处理测试脚本
 * 用于验证各种错误场景下的处理机制
 */

// 模拟不同类型的错误
const testErrors = [
  {
    name: '网络错误 (502 Bad Gateway)',
    error: {
      status: 502,
      message: '[GoogleGenerativeAI Error]: Error fetching from http://ai-coding-v2.sit.saas.htsc/gemini/v1beta/models/gemini-2.5-flash:streamGenerateContent?alt=sse: [502 Bad Gateway]',
      attemptNumber: 7,
      retriesLeft: 0
    }
  },
  {
    name: '速率限制错误 (429)',
    error: {
      status: 429,
      message: 'Rate limit exceeded. Please try again later.'
    }
  },
  {
    name: '超时错误',
    error: {
      message: 'Request timeout after 30 seconds'
    }
  },
  {
    name: '网络连接错误',
    error: {
      message: 'Failed to fetch: network error'
    }
  },
  {
    name: 'API 错误 (400)',
    error: {
      status: 400,
      message: 'Invalid request format'
    }
  }
];

// 如果在 Node.js 环境中运行
if (typeof require !== 'undefined') {
  const { analyzeError, formatErrorForToast, isRetryableError, getRetryDelay } = require('./src/utils/error-handler');
  
  console.log('🧪 错误处理测试开始\n');
  
  testErrors.forEach((test, index) => {
    console.log(`${index + 1}. ${test.name}`);
    console.log('   原始错误:', JSON.stringify(test.error, null, 2));
    
    const analysis = analyzeError(test.error);
    console.log('   错误分析:', {
      type: analysis.type,
      title: analysis.title,
      isRetryable: analysis.isRetryable,
      retryAfter: analysis.retryAfter
    });
    
    const toastFormat = formatErrorForToast(test.error);
    console.log('   Toast 格式:', toastFormat);
    
    console.log('   可重试:', isRetryableError(test.error));
    console.log('   重试延迟:', getRetryDelay(test.error), '秒');
    console.log('');
  });
  
  console.log('✅ 错误处理测试完成');
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
  console.log('🌐 浏览器环境错误处理测试');
  
  // 模拟 API 调用错误
  window.testErrorHandling = function() {
    testErrors.forEach((test, index) => {
      console.group(`测试 ${index + 1}: ${test.name}`);
      
      // 这里可以添加实际的错误处理测试逻辑
      console.log('模拟错误:', test.error);
      
      // 如果有错误处理函数，可以在这里调用
      // const result = handleError(test.error);
      // console.log('处理结果:', result);
      
      console.groupEnd();
    });
  };
  
  console.log('运行 window.testErrorHandling() 来测试错误处理');
}

// 导出测试数据供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testErrors,
    runTests: function() {
      console.log('运行错误处理测试...');
      // 测试逻辑
    }
  };
}
