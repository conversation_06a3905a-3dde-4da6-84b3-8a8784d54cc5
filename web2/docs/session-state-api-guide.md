# LangGraph 会话状态查询 API 使用指南

## 概述

本指南介绍如何使用新增的会话状态查询功能，解决会话数据不完整和缺少具体message信息的问题。

## 问题解决方案

### 1. 会话数据不完整问题

**问题**: 从截图可以看到历史记录中只显示了检查点信息，但缺少具体的message内容。

**解决方案**: 使用新的API参数获取完整的会话状态和消息历史。

### 2. 查询checkpointer状态

**问题**: 需要查询当前graph的状态和检查点信息。

**解决方案**: 提供了多个API端点来查询和诊断会话状态。

## API 使用示例

### 1. 获取完整会话信息（推荐）

```bash
# 获取包含状态、消息和历史的完整信息
GET /api/sessions?thread_id=YOUR_THREAD_ID&include_state=true&include_messages=true&include_history=true
```

**响应示例**:
```json
{
  "session": {
    "thread_id": "e0876604-cf27-462e-9774-c4bdc8dc8df",
    "title": "你一个实例",
    "status": "active",
    "message_count": 2,
    "created_at": "2025/01/13:35:05"
  },
  "current_state": {
    "checkpoint_id": "1ef...",
    "timestamp": "2025/01/13:35:05",
    "state": {
      "messages": [...],
      "input": "用户输入",
      "output": "AI回复"
    }
  },
  "messages": [
    {
      "id": 0,
      "type": "human",
      "content": "你好，我是测试用户",
      "role": "user",
      "timestamp": "2025/01/13:35:05"
    },
    {
      "id": 1,
      "type": "ai", 
      "content": "你好！我是AI助手...",
      "role": "assistant",
      "timestamp": "2025/01/13:35:05"
    }
  ],
  "recent_checkpoints": [...],
  "summary": {
    "total_messages": 2,
    "total_checkpoints": 3,
    "last_activity": "2025/01/13:35:05",
    "has_active_state": true
  }
}
```

### 2. 仅获取消息历史

```bash
# 只获取消息内容，解决"会话数据不完整"问题
GET /api/sessions?thread_id=YOUR_THREAD_ID&include_messages=true&message_limit=50
```

### 3. 查询当前状态

```bash
# 查询当前LangGraph状态
GET /api/sessions/YOUR_THREAD_ID/state?action=current
```

### 4. 获取检查点历史

```bash
# 获取详细的检查点历史
GET /api/sessions/YOUR_THREAD_ID/state?action=history&limit=20
```

### 5. 诊断会话问题

```bash
# 诊断会话状态，获取优化建议
GET /api/debug/sessions?thread_id=YOUR_THREAD_ID
```

**诊断响应示例**:
```json
{
  "type": "session_diagnostics",
  "data": {
    "thread_id": "e0876604-cf27-462e-9774-c4bdc8dc8df",
    "session_exists": true,
    "checkpoint_exists": true,
    "message_count": 2,
    "state_summary": {
      "has_messages": true,
      "has_input": true,
      "has_output": true,
      "state_keys": ["messages", "input", "output"]
    },
    "recommendations": [
      "会话状态正常，无需特殊处理"
    ]
  }
}
```

## 最佳实践

### 1. 解决消息显示问题

如果您的界面只显示检查点信息而没有具体消息内容，请使用：

```javascript
// 前端代码示例
async function loadSessionWithMessages(threadId) {
  const response = await fetch(
    `/api/sessions?thread_id=${threadId}&include_messages=true&include_state=true`
  );
  const data = await response.json();
  
  // 现在可以访问完整的消息历史
  const messages = data.messages || [];
  const currentState = data.current_state;
  
  return { messages, currentState };
}
```

### 2. 监控会话健康状态

```javascript
// 定期检查会话状态
async function monitorSessionHealth(threadId) {
  const response = await fetch(`/api/debug/sessions?thread_id=${threadId}`);
  const diagnostics = await response.json();
  
  if (diagnostics.data.recommendations.length > 1) {
    console.warn('会话需要注意:', diagnostics.data.recommendations);
  }
  
  return diagnostics.data;
}
```

### 3. 处理状态同步问题

```javascript
// 如果发现状态不同步，可以尝试修复
async function repairSessionIfNeeded(threadId) {
  const diagnostics = await fetch(`/api/debug/sessions?thread_id=${threadId}`);
  const data = await diagnostics.json();
  
  if (!data.data.checkpoint_exists && data.data.session_exists) {
    // 尝试修复
    const repairResponse = await fetch('/api/debug/sessions', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        thread_id: threadId,
        action: 'repair',
        options: {
          recreateSession: true
        }
      })
    });
    
    return await repairResponse.json();
  }
}
```

## 性能优化建议

### 1. 按需加载数据

- 列表页面：只获取基本会话信息
- 详情页面：根据需要加载状态和消息
- 调试页面：使用诊断API

### 2. 限制数据量

```bash
# 限制消息数量，避免加载过多历史数据
GET /api/sessions?thread_id=XXX&include_messages=true&message_limit=20

# 限制检查点历史
GET /api/sessions?thread_id=XXX&include_history=true&history_limit=5
```

### 3. 缓存策略

- 会话基本信息可以缓存较长时间
- 消息和状态信息需要实时获取
- 诊断信息可以按需获取

## 故障排除

### 常见问题

1. **消息为空**: 检查 `include_messages=true` 参数
2. **状态缺失**: 检查 `include_state=true` 参数  
3. **检查点不存在**: 使用诊断API查看具体原因
4. **性能问题**: 减少 `message_limit` 和 `history_limit`

### 调试步骤

1. 使用诊断API检查会话状态
2. 查看具体的错误信息和建议
3. 根据建议进行修复
4. 重新验证会话状态

## 总结

通过这些新的API功能，您可以：

1. ✅ 获取完整的消息历史，解决"会话数据不完整"问题
2. ✅ 查询当前checkpointer的graph状态
3. ✅ 诊断和修复会话问题
4. ✅ 监控会话健康状态
5. ✅ 优化性能和用户体验

这些改进基于LangGraph官方文档的最佳实践，提供了全面的会话状态管理解决方案。
