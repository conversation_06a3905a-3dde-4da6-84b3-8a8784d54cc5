# 错误处理机制说明

## 概述

本项目实现了一套完整的错误处理机制，能够优雅地处理 LLM 调用失败等各种错误情况，提供用户友好的错误提示和自动重试功能。

## 错误处理架构

### 1. 多层错误处理

```
用户界面 (ChatWindow)
    ↓
API 路由 (route.ts)
    ↓
LangGraph 节点 (simpleChat)
    ↓
模型调用 (Google Gemini / OpenAI)
```

每一层都有相应的错误处理机制，确保错误能够被正确捕获和处理。

### 2. 错误类型分类

- **网络错误** (502, 503, 504): 服务器连接问题
- **速率限制错误** (429): 请求过于频繁
- **超时错误**: 请求响应时间过长
- **API 错误** (4xx): 请求格式或内容错误
- **服务不可用**: 服务维护或故障
- **未知错误**: 其他未分类错误

### 3. 错误处理策略

#### 自动重试机制
- 网络错误: 自动重试，使用备用模型
- 超时错误: 延迟重试
- 速率限制: 按指定时间延迟重试
- API 错误: 不重试，提示用户修改输入

#### 备用模型切换
当主模型 (Google Gemini) 失败时，自动切换到备用模型 (OpenAI)：

```typescript
// 主模型失败时的处理
if (errorAnalysis.isRetryable) {
  const fallbackResult = await tryFallbackModel(messages);
  if (fallbackResult.success) {
    return fallbackResult.response;
  }
}
```

## 用户体验优化

### 1. 友好的错误消息

根据错误类型提供具体的用户指导：

- **网络错误**: "服务器暂时无法响应，请稍后再试"
- **速率限制**: "请求过于频繁，请稍等片刻再发送消息"
- **输入错误**: "请检查您的输入内容并重新发送"

### 2. 智能重试建议

每种错误类型都提供相应的解决建议：

```typescript
suggestions: [
  '等待几分钟后重试',
  '检查网络连接',
  '如果问题持续，请联系技术支持'
]
```

### 3. 可视化错误状态

- Toast 通知显示错误信息
- 错误指示器组件显示详细信息
- 重试按钮提供快速恢复选项

## 实现细节

### 1. 错误分析工具 (`error-handler.ts`)

```typescript
export function analyzeError(error: any): ErrorInfo {
  // 分析错误类型、严重程度和重试策略
}
```

### 2. LangGraph 节点错误处理

```typescript
async function simpleChatNode(state) {
  try {
    // 尝试主模型
    const model = createModel();
    return await model.invoke(messages);
  } catch (error) {
    // 分析错误并尝试备用方案
    if (errorAnalysis.isRetryable) {
      return await tryFallbackModel(messages);
    }
    // 返回用户友好的错误消息
  }
}
```

### 3. API 路由错误处理

```typescript
export async function POST(req: NextRequest) {
  try {
    // 处理请求
  } catch (e) {
    return createErrorResponse(e, '请求处理');
  }
}
```

### 4. 前端错误处理

```typescript
const chat = useChat({
  onError: (e: Error) => {
    const errorInfo = formatErrorForToast(e);
    toast.error(errorInfo.title, {
      description: errorInfo.description,
      action: canRetry ? { label: '重试', onClick: retry } : undefined
    });
  }
});
```

## 配置选项

### 重试配置

```typescript
const RETRY_CONFIG = {
  maxRetries: 3,
  networkErrorDelay: 30, // 秒
  rateLimitDelay: 60,    // 秒
  timeoutDelay: 10       // 秒
};
```

### 模型配置

```typescript
// 主模型: Google Gemini
const primaryModel = createModel("google");

// 备用模型: OpenAI
const fallbackModel = createModel("openai");
```

## 监控和日志

### 错误日志记录

```typescript
console.error("SimpleChat: 模型调用失败:", {
  error: error.message,
  status: error.status,
  attemptNumber: error.attemptNumber,
  retriesLeft: error.retriesLeft
});
```

### 性能监控

- 记录错误发生频率
- 监控重试成功率
- 跟踪备用模型使用情况

## 最佳实践

1. **渐进式降级**: 从主模型到备用模型的平滑切换
2. **用户透明**: 错误处理对用户尽可能透明
3. **快速恢复**: 提供多种恢复选项
4. **详细日志**: 便于问题诊断和优化
5. **用户教育**: 提供清晰的错误说明和解决建议

## 测试

运行错误处理测试：

```bash
npm test error-handler.test.ts
```

测试覆盖：
- 各种错误类型的识别
- 重试策略的正确性
- 用户消息的友好性
- 备用模型切换逻辑
