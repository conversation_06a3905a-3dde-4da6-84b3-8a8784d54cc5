{"name": "langgraph-mvp-web", "version": "0.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write \"src\""}, "engines": {"node": ">=18"}, "dependencies": {"@langchain/community": "0.3.53", "@langchain/core": "0.3.72", "@langchain/google-genai": "^0.2.16", "@langchain/langgraph": "0.4.7", "@langchain/langgraph-checkpoint-mongodb": "^0.1.1", "@langchain/openai": "0.5.7", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.13", "ai": "4.1.66", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "langchain": "^0.3.19", "lucide-react": "^0.475.0", "marked": "^15.0.7", "mongodb": "^6.19.0", "next": "15.2.4", "next-themes": "^0.4.4", "react": "19.0.0", "react-dom": "19.0.0", "react-markdown": "^10.0.0", "react-toastify": "11.0.3", "remark-gfm": "^4.0.1", "sonner": "^1.7.2", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "use-stick-to-bottom": "^1.0.44", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.24.2", "zod-to-json-schema": "^3.23.2"}, "devDependencies": {"@next/bundle-analyzer": "^15.1.7", "@types/node": "^22.13.4", "@types/react": "19.0.9", "@types/react-dom": "19.0.3", "autoprefixer": "^10.4.20", "eslint": "^9.20.1", "eslint-config-next": "^15.1.7", "postcss": "8.5.2", "prettier": "^3.4.2", "tailwindcss": "3.4.17", "typescript": "5.7.3"}}