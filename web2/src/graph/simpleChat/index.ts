import { StateGraph, END, START, Annotation } from "@langchain/langgraph";
import { SystemMessage, HumanMessage } from "@langchain/core/messages";
import type { BaseMessageLike } from "@langchain/core/messages";
import { createModel } from "../designToCode/model/index";
import { createOpenaiModel } from "../designToCode/model/openai";
import { getMongoCheckpointer } from "@/config/mongodb";

// 定义模型配置类型
interface ModelConfig {
  type: 'google' | 'openai';
  providerType?: 'htsc' | 'openrouter' | 'local';
}

// 定义错误类型
interface ModelError {
  type: 'network' | 'rate_limit' | 'timeout' | 'api_error' | 'unknown';
  isRetryable: boolean;
  canSwitchModel: boolean;
  originalError: any;
}

// 备用模型配置
const FALLBACK_MODELS: ModelConfig[] = [
  { type: 'openai', providerType: 'htsc' },
  { type: 'openai', providerType: 'openrouter' },
  { type: 'openai', providerType: 'local' },
  { type: 'google' }
];

// 重试配置
const RETRY_CONFIG = {
  maxRetries: 2,
  baseDelay: 1000, // 1秒
  maxDelay: 10000, // 10秒
  backoffMultiplier: 2
};

// 错误分析函数
function analyzeError(error: any): ModelError {
  const errorMessage = error?.message || '';
  const status = error?.status || error?.statusCode;

  // 网络错误
  if (status === 502 || status === 503 || status === 504 ||
      errorMessage.includes('fetch') || errorMessage.includes('network')) {
    return {
      type: 'network',
      isRetryable: true,
      canSwitchModel: true,
      originalError: error
    };
  }

  // 速率限制
  if (status === 429 || errorMessage.includes('rate limit')) {
    return {
      type: 'rate_limit',
      isRetryable: true,
      canSwitchModel: true,
      originalError: error
    };
  }

  // 超时错误
  if (errorMessage.includes('timeout') || errorMessage.includes('TIMEOUT')) {
    return {
      type: 'timeout',
      isRetryable: true,
      canSwitchModel: true,
      originalError: error
    };
  }

  // API错误
  if (status >= 400 && status < 500) {
    return {
      type: 'api_error',
      isRetryable: false,
      canSwitchModel: true,
      originalError: error
    };
  }

  // 未知错误
  return {
    type: 'unknown',
    isRetryable: true,
    canSwitchModel: true,
    originalError: error
  };
}

// 延迟函数
function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 获取下一个备用模型
function getNextFallbackModel(currentConfig: ModelConfig): ModelConfig | null {
  const currentIndex = FALLBACK_MODELS.findIndex(
    model => model.type === currentConfig.type &&
    model.providerType === currentConfig.providerType
  );

  const nextIndex = currentIndex + 1;
  return nextIndex < FALLBACK_MODELS.length ? FALLBACK_MODELS[nextIndex] : null;
}

const SimpleChatStateAnnotation = Annotation.Root({
  messages: Annotation<BaseMessageLike[]>({
    reducer: (x, y) => x.concat(y),
    default: () => [],
  }),
  input: Annotation<string>({
    reducer: (x, y) => y || x,
    default: () => "",
  }),
  output: Annotation<string>({
    reducer: (x, y) => y || x,
    default: () => "",
  }),
  modelConfig: Annotation<ModelConfig>({
    reducer: (x, y) => y || x,
    default: () => ({ type: 'google' }),
  }),
  lastError: Annotation<string>({
    reducer: (x, y) => y || x,
    default: () => "",
  }),
  retryCount: Annotation<number>({
    reducer: (x, y) => y || x,
    default: () => 0,
  }),
});

/**
 * 尝试使用指定模型调用 - 直接返回模型响应以支持流式输出
 */
async function tryModelInvoke(modelConfig: ModelConfig, messages: BaseMessageLike[]): Promise<any> {
  let model;

  if (modelConfig.type === 'openai') {
    model = createOpenaiModel(modelConfig.providerType);
  } else {
    model = createModel('google');
  }

  console.log(`SimpleChat: 使用模型类型: ${modelConfig.type}${modelConfig.providerType ? ` (${modelConfig.providerType})` : ''}`);

  // 直接调用模型，让 LangChain 的 streamEvents 处理流式输出
  return await model.invoke(messages, { timeout: 15000 });
}

/**
 * 简单的聊天节点 - 增强版错误处理
 */
async function simpleChatNode(state: typeof SimpleChatStateAnnotation.State) {
  console.log("SimpleChat: 处理用户消息...", {
    messagesCount: state.messages.length,
    input: state.input,
    retryCount: state.retryCount || 0
  });

  // 构建消息列表 - 使用现有的消息历史
  let messages = [...state.messages];

  // 如果没有消息历史，添加系统消息
  if (messages.length === 0) {
    messages.push(new SystemMessage("你是一个友好的AI助手。请简洁地回复用户的问题。"));
  }

  // 如果有额外的输入，添加为用户消息
  if (state.input && state.input.trim()) {
    messages.push(new HumanMessage(state.input));
  }

  console.log(`SimpleChat: 调用模型处理 ${messages.length} 条消息`);

  const currentModelConfig = state.modelConfig || { type: 'google' };
  const retryCount = state.retryCount || 0;

  try {
    // 尝试当前模型
    const responseMessage = await tryModelInvoke(currentModelConfig, messages);

    console.log("SimpleChat: 模型响应成功", {
      responseLength: responseMessage.content?.toString().length || 0,
      modelUsed: `${currentModelConfig.type}${currentModelConfig.providerType ? ` (${currentModelConfig.providerType})` : ''}`
    });

    return {
      messages: [responseMessage],
      output: responseMessage.content?.toString() || "",
      retryCount: 0, // 重置重试计数
      lastError: "", // 清除错误信息
    };
  } catch (error) {
    console.error("SimpleChat: 模型调用失败:", error);

    const errorAnalysis = analyzeError(error);
    const errorMessage = error instanceof Error ? error.message : String(error);

    // 如果可以切换模型且还有备用模型
    if (errorAnalysis.canSwitchModel && retryCount < RETRY_CONFIG.maxRetries) {
      const nextModel = getNextFallbackModel(currentModelConfig);

      if (nextModel) {
        console.log(`SimpleChat: 尝试切换到备用模型: ${nextModel.type}${nextModel.providerType ? ` (${nextModel.providerType})` : ''}`);

        // 添加延迟以避免过快重试
        const delayTime = Math.min(
          RETRY_CONFIG.baseDelay * Math.pow(RETRY_CONFIG.backoffMultiplier, retryCount),
          RETRY_CONFIG.maxDelay
        );

        if (delayTime > 0) {
          await delay(delayTime);
        }

        try {
          const responseMessage = await tryModelInvoke(nextModel, messages);

          console.log("SimpleChat: 备用模型响应成功", {
            responseLength: responseMessage.content?.toString().length || 0,
            fallbackModel: `${nextModel.type}${nextModel.providerType ? ` (${nextModel.providerType})` : ''}`,
            retryCount: retryCount + 1
          });

          return {
            messages: [responseMessage],
            output: responseMessage.content?.toString() || "",
            modelConfig: nextModel, // 更新为成功的模型配置
            retryCount: 0, // 重置重试计数
            lastError: "", // 清除错误信息
          };
        } catch (fallbackError) {
          console.error("SimpleChat: 备用模型也失败:", fallbackError);

          // 如果还有重试机会，继续尝试下一个模型
          if (retryCount + 1 < RETRY_CONFIG.maxRetries) {
            const fallbackErrorMessage = fallbackError instanceof Error ? fallbackError.message : String(fallbackError);
            return {
              retryCount: retryCount + 1,
              lastError: `主模型和备用模型都失败: ${errorMessage}; ${fallbackErrorMessage}`,
              modelConfig: nextModel, // 更新模型配置以便下次重试
            };
          }
        }
      }
    }

    // 所有重试都失败，抛出详细错误
    const detailedError = new Error(
      `所有模型都调用失败。最后尝试的模型: ${currentModelConfig.type}${currentModelConfig.providerType ? ` (${currentModelConfig.providerType})` : ''}。错误: ${errorMessage}`
    );

    // 添加错误类型信息以便前端处理
    (detailedError as any).errorType = errorAnalysis.type;
    (detailedError as any).isRetryable = errorAnalysis.isRetryable;
    (detailedError as any).canSwitchModel = errorAnalysis.canSwitchModel;
    (detailedError as any).retryCount = retryCount;

    throw detailedError;
  }
}

// 构建简单聊天工作流图
const workflow = new StateGraph(SimpleChatStateAnnotation)
  .addNode("chat", simpleChatNode)
  .addEdge(START, "chat")
  .addEdge("chat", END);

// 编译工作流 - 异步函数以支持checkpointer
let compiledGraph: any = null;

export async function getSimpleChatGraph() {
  if (!compiledGraph) {
    try {
      console.log("SimpleChat: 初始化MongoDB checkpointer...");
      const checkpointer = await getMongoCheckpointer();

      // 使用checkpointer编译工作流
      compiledGraph = workflow.compile({ checkpointer });
      compiledGraph.name = "simpleChat";

      console.log("SimpleChat: 图已创建并编译完成（带持久化支持）");
    } catch (error) {
      console.error("SimpleChat: 初始化checkpointer失败，使用内存模式", error);
      // 如果MongoDB连接失败，回退到内存模式
      compiledGraph = workflow.compile();
      compiledGraph.name = "simpleChat";

      console.log("SimpleChat: 图已创建并编译完成（内存模式）");
    }
  }

  return compiledGraph;
}

// 为了向后兼容，保留同步导出（但建议使用异步版本）
export const graph = workflow.compile();
graph.name = "simpleChat";
