import { BaseMessage, AIMessage } from "@langchain/core/messages";
export const isHtml = (content: string) => {
  return (
    content &&
    (content.includes("<html") ||
      content.includes("<!DOCTYPE") ||
      content.includes("<div") ||
      content.includes("<body"))
  );
};

export const isImg = (content: string) => {
  if (!content) return false;

  // 检查图片URL
  if (
    content.includes("http") &&
    (content.includes(".jpg") ||
      content.includes(".jpeg") ||
      content.includes(".png") ||
      content.includes(".gif") ||
      content.includes(".webp"))
  ) {
    return true;
  }

  // 检查base64图片数据
  if (content.includes("data:image")) {
    return true;
  }

  return false;
};

export const printLog = (messages: BaseMessage[]) => {
  console.log(
    "发送给模型的消息:",
    JSON.stringify(
      messages.map((m) => ({
        type: m.constructor.name,
        content:
          typeof m.content === "string"
            ? m.content.slice(0, 100)
            : "complex content",
      })),
      null,
      2
    )
  );
};
/**
 * 从工具调用结果中提取HTML代码
 * 主要用于处理文件写入工具的结果
 */
export function extractHtmlFromToolResult(message: AIMessage): string {
  if (!message.tool_calls || message.tool_calls.length === 0) {
    return "";
  }

  // 查找文件写入工具的结果
  for (const toolCall of message.tool_calls) {
    if (toolCall.name === "writeFile" && toolCall.args) {
      try {
        const args =
          typeof toolCall.args === "string"
            ? JSON.parse(toolCall.args)
            : toolCall.args;

        // 如果写入的是HTML文件，返回其内容
        if (args.fileName && args.fileName.endsWith(".html")) {
          return args.content || "";
        }
      } catch (error) {
        console.warn("解析工具调用参数失败:", error);
      }
    }
  }

  return "";
}
