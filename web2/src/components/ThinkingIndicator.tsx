'use client';

import { cn } from '@/utils/cn';

interface ThinkingIndicatorProps {
  isVisible: boolean;
  className?: string;
  text?: string;
}

// 简洁的加载动画组件
function LoadingDots() {
  return (
    <div className="flex space-x-1">
      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
    </div>
  );
}

export function ThinkingIndicator({
  isVisible,
  className,
  text
}: ThinkingIndicatorProps) {
  if (!isVisible) return null;

  return (
    <div className={cn(
      "flex justify-start items-center space-x-3 px-4 py-3 bg-gray-50 rounded-lg border border-gray-200",
      className
    )}>
      <LoadingDots />
      { text ? <span className="text-sm text-gray-600">{text}</span> : null}
    </div>
  );
}
