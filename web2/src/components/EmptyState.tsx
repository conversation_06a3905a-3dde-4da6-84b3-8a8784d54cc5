import { cn } from '@/utils/cn';

interface FeatureCard {
  icon: string;
  title: string;
  description: string;
  gradient: string;
  borderColor: string;
  textColor: string;
  bgGradient: string;
}

interface EmptyStateProps {
  emoji: string;
  title: string;
  description: string;
  features: FeatureCard[];
  capabilities?: string[];
  tips?: string[];
  className?: string;
}

export function EmptyState({
  emoji,
  title,
  description,
  features,
  capabilities,
  tips,
  className
}: EmptyStateProps) {
  return (
    <div className={cn("flex flex-col items-center justify-center h-full text-center p-8 animate-fade-in", className)}>
      <div className="relative mb-8">
        <div className="text-8xl mb-4 animate-bounce">{emoji}</div>
        <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full animate-pulse"></div>
        {emoji === "🤖" && (
          <div className="absolute -bottom-2 -left-2 w-4 h-4 bg-blue-500 rounded-full animate-ping"></div>
        )}
      </div>
      
      <h1 className="text-3xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
        {title}
      </h1>
      
      <p className="text-gray-600 mb-6 text-lg max-w-md">
        {description}
      </p>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl w-full mb-8">
        {features.map((feature, index) => (
          <div
            key={index}
            className={cn(
              "rounded-2xl p-6 border hover:shadow-lg transition-all duration-300 hover:scale-105",
              feature.bgGradient,
              feature.borderColor
            )}
          >
            <div className="text-2xl mb-2">{feature.icon}</div>
            <h3 className={cn("font-semibold mb-2", feature.textColor)}>
              {feature.title}
            </h3>
            <p className={cn("text-sm", feature.textColor.replace('800', '600'))}>
              {feature.description}
            </p>
          </div>
        ))}
      </div>
      
      {capabilities && (
        <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-2xl p-6 border border-gray-200 max-w-lg w-full mb-6">
          <h3 className="font-semibold mb-3 text-gray-800">支持的功能</h3>
          <div className="grid grid-cols-2 gap-3 text-sm">
            {capabilities.map((capability, index) => (
              <div key={index} className="flex items-center gap-2">
                <span className={cn(
                  "w-2 h-2 rounded-full",
                  index % 4 === 0 && "bg-green-500",
                  index % 4 === 1 && "bg-blue-500", 
                  index % 4 === 2 && "bg-purple-500",
                  index % 4 === 3 && "bg-orange-500"
                )}></span>
                <span className="text-gray-600">{capability}</span>
              </div>
            ))}
          </div>
        </div>
      )}
      
      {tips && (
        <div className="text-sm text-gray-500 space-y-1">
          {tips.map((tip, index) => (
            <p key={index}>{tip}</p>
          ))}
        </div>
      )}
    </div>
  );
}
