'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { AlertCircle, CheckCircle, RefreshCw, MessageSquare, Database, Clock } from 'lucide-react';

interface SessionStateViewerProps {
  threadId: string;
}

interface SessionData {
  session: any;
  current_state?: any;
  messages?: any[];
  recent_checkpoints?: any[];
  summary?: any;
}

interface DiagnosticsData {
  thread_id: string;
  session_exists: boolean;
  checkpoint_exists: boolean;
  message_count: number;
  state_summary: {
    has_messages: boolean;
    has_input: boolean;
    has_output: boolean;
    state_keys: string[];
  };
  recommendations: string[];
}

export default function SessionStateViewer({ threadId }: SessionStateViewerProps) {
  const [sessionData, setSessionData] = useState<SessionData | null>(null);
  const [diagnostics, setDiagnostics] = useState<DiagnosticsData | null>(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  // 加载完整会话数据
  const loadSessionData = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `/api/sessions?thread_id=${threadId}&include_state=true&include_messages=true&include_history=true&message_limit=50`
      );
      const data = await response.json();
      setSessionData(data);
    } catch (error) {
      console.error('加载会话数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 加载诊断信息
  const loadDiagnostics = async () => {
    try {
      const response = await fetch(`/api/debug/sessions?thread_id=${threadId}`);
      const result = await response.json();
      setDiagnostics(result.data);
    } catch (error) {
      console.error('加载诊断信息失败:', error);
    }
  };

  // 修复会话
  const repairSession = async () => {
    try {
      const response = await fetch('/api/debug/sessions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          thread_id: threadId,
          action: 'repair',
          options: {
            recreateSession: true,
            clearCheckpoints: false
          }
        })
      });
      const result = await response.json();
      
      if (result.result.success) {
        // 重新加载数据
        await Promise.all([loadSessionData(), loadDiagnostics()]);
      }
    } catch (error) {
      console.error('修复会话失败:', error);
    }
  };

  useEffect(() => {
    if (threadId) {
      loadSessionData();
      loadDiagnostics();
    }
  }, [threadId]);

  const getStatusBadge = (status: string) => {
    const variants: Record<string, 'default' | 'secondary' | 'destructive'> = {
      active: 'default',
      archived: 'secondary',
      deleted: 'destructive'
    };
    return <Badge variant={variants[status] || 'secondary'}>{status}</Badge>;
  };

  const getHealthIcon = (isHealthy: boolean) => {
    return isHealthy ? (
      <CheckCircle className="w-4 h-4 text-green-500" />
    ) : (
      <AlertCircle className="w-4 h-4 text-red-500" />
    );
  };

  if (loading && !sessionData) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="w-6 h-6 animate-spin mr-2" />
        <span>加载会话状态...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 会话概览 */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Database className="w-5 h-5" />
            会话状态概览
          </CardTitle>
          <Button onClick={() => Promise.all([loadSessionData(), loadDiagnostics()])} size="sm">
            <RefreshCw className="w-4 h-4 mr-1" />
            刷新
          </Button>
        </CardHeader>
        <CardContent>
          {sessionData?.session && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <div className="text-sm text-gray-500">会话ID</div>
                <div className="font-mono text-xs">{sessionData.session.thread_id}</div>
              </div>
              <div>
                <div className="text-sm text-gray-500">状态</div>
                <div>{getStatusBadge(sessionData.session.status)}</div>
              </div>
              <div>
                <div className="text-sm text-gray-500">消息数量</div>
                <div className="flex items-center gap-1">
                  <MessageSquare className="w-4 h-4" />
                  {sessionData.summary?.total_messages || 0}
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-500">最后活动</div>
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  {new Date(sessionData.summary?.last_activity || sessionData.session.last_accessed).toLocaleString()}
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 详细信息标签页 */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="messages">消息历史</TabsTrigger>
          <TabsTrigger value="state">当前状态</TabsTrigger>
          <TabsTrigger value="diagnostics">诊断</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>会话摘要</CardTitle>
            </CardHeader>
            <CardContent>
              {sessionData?.summary && (
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>总消息数:</span>
                    <span>{sessionData.summary.total_messages}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>检查点数:</span>
                    <span>{sessionData.summary.total_checkpoints}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>状态活跃:</span>
                    <span>{getHealthIcon(sessionData.summary.has_active_state)}</span>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="messages" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>消息历史</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {sessionData?.messages?.map((message, index) => (
                  <div key={index} className={`p-3 rounded-lg ${
                    message.role === 'user' ? 'bg-blue-50 ml-8' : 'bg-gray-50 mr-8'
                  }`}>
                    <div className="flex justify-between items-start mb-1">
                      <Badge variant={message.role === 'user' ? 'default' : 'secondary'}>
                        {message.role}
                      </Badge>
                      <span className="text-xs text-gray-500">
                        {new Date(message.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                    <div className="text-sm">{message.content}</div>
                  </div>
                )) || <div className="text-gray-500">暂无消息</div>}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="state" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>当前LangGraph状态</CardTitle>
            </CardHeader>
            <CardContent>
              {sessionData?.current_state ? (
                <div className="space-y-4">
                  <div>
                    <div className="text-sm font-medium">检查点ID:</div>
                    <div className="font-mono text-xs bg-gray-100 p-2 rounded">
                      {sessionData.current_state.checkpoint_id}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm font-medium">状态键:</div>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {Object.keys(sessionData.current_state.state || {}).map(key => (
                        <Badge key={key} variant="outline">{key}</Badge>
                      ))}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm font-medium">状态数据:</div>
                    <pre className="text-xs bg-gray-100 p-3 rounded overflow-auto max-h-48">
                      {JSON.stringify(sessionData.current_state.state, null, 2)}
                    </pre>
                  </div>
                </div>
              ) : (
                <div className="text-gray-500">无当前状态数据</div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="diagnostics" className="space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>诊断信息</CardTitle>
              {diagnostics && diagnostics.recommendations.length > 1 && (
                <Button onClick={repairSession} size="sm" variant="outline">
                  修复会话
                </Button>
              )}
            </CardHeader>
            <CardContent>
              {diagnostics && (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center gap-2">
                      <span>会话存在:</span>
                      {getHealthIcon(diagnostics.session_exists)}
                    </div>
                    <div className="flex items-center gap-2">
                      <span>检查点存在:</span>
                      {getHealthIcon(diagnostics.checkpoint_exists)}
                    </div>
                    <div className="flex items-center gap-2">
                      <span>包含消息:</span>
                      {getHealthIcon(diagnostics.state_summary.has_messages)}
                    </div>
                    <div className="flex items-center gap-2">
                      <span>消息数量:</span>
                      <span>{diagnostics.message_count}</span>
                    </div>
                  </div>
                  
                  <div>
                    <div className="text-sm font-medium mb-2">建议:</div>
                    <div className="space-y-1">
                      {diagnostics.recommendations.map((rec, index) => (
                        <div key={index} className={`text-sm p-2 rounded ${
                          rec.includes('正常') ? 'bg-green-50 text-green-700' : 'bg-yellow-50 text-yellow-700'
                        }`}>
                          {rec}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
