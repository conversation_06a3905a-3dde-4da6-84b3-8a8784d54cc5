'use client';

import { useState } from 'react';
import { AlertCircle, RefreshCw, Zap, Clock, Wifi, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/utils/cn';
import { type ModelConfig } from '@/components/ModelSelector';

interface ErrorMessageBubbleProps {
  error: {
    type: 'network' | 'rate_limit' | 'timeout' | 'api_error' | 'unknown';
    message: string;
    isRetryable: boolean;
    canSwitchModel: boolean;
    retryCount?: number;
  };
  currentModel: ModelConfig;
  onRetry: () => void;
  onSwitchModel: () => void;
  isRetrying?: boolean;
  className?: string;
}

// 错误类型配置
const ERROR_CONFIG = {
  network: {
    icon: Wifi,
    title: '网络连接问题',
    color: 'text-orange-600',
    bgColor: 'bg-orange-50',
    borderColor: 'border-orange-200',
    description: '无法连接到服务器'
  },
  rate_limit: {
    icon: Clock,
    title: '请求过于频繁',
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-50',
    borderColor: 'border-yellow-200',
    description: '请稍等片刻再试'
  },
  timeout: {
    icon: Clock,
    title: '请求超时',
    color: 'text-blue-600',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    description: '服务器响应时间过长'
  },
  api_error: {
    icon: AlertTriangle,
    title: 'API调用错误',
    color: 'text-red-600',
    bgColor: 'bg-red-50',
    borderColor: 'border-red-200',
    description: '请求格式或内容有误'
  },
  unknown: {
    icon: AlertCircle,
    title: '出现了问题',
    color: 'text-gray-600',
    bgColor: 'bg-gray-50',
    borderColor: 'border-gray-200',
    description: '遇到了一些技术问题'
  }
};

// 获取模型显示名称
function getModelDisplayName(config: ModelConfig): string {
  if (config.type === 'google') {
    return 'Google Gemini';
  }
  const providerNames = {
    htsc: 'HTSC DeepSeek',
    openrouter: 'OpenRouter Gemini',
    local: 'Local Model'
  };
  return providerNames[config.providerType || 'htsc'] || 'Unknown Model';
}

export function ErrorMessageBubble({
  error,
  currentModel,
  onRetry,
  onSwitchModel,
  isRetrying = false,
  className
}: ErrorMessageBubbleProps) {
  const [isRetryingLocal, setIsRetryingLocal] = useState(false);
  const [isSwitching, setIsSwitching] = useState(false);

  const config = ERROR_CONFIG[error.type];
  const Icon = config.icon;

  const handleRetry = async () => {
    setIsRetryingLocal(true);
    try {
      await onRetry();
    } finally {
      setIsRetryingLocal(false);
    }
  };

  const handleSwitchModel = async () => {
    setIsSwitching(true);
    try {
      await onSwitchModel();
    } finally {
      setIsSwitching(false);
    }
  };

  const showRetrying = isRetrying || isRetryingLocal;

  return (
    <div className={cn("mb-6", className)}>
      <div className="flex items-start justify-start max-w-[100%]">
        {/* 错误图标 */}
        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-red-100 flex items-center justify-center mr-3">
          <Icon className="w-4 h-4 text-red-600" />
        </div>

        {/* 错误消息内容 */}
        <div className={cn(
          "rounded-2xl px-4 py-3 max-w-[80%] border-2",
          config.bgColor,
          config.borderColor
        )}>
          <div className="space-y-3">
            {/* 错误标题和描述 */}
            <div>
              <div className={cn("font-medium text-sm", config.color)}>
                {config.title}
                {error.retryCount && error.retryCount > 0 && (
                  <span className="ml-2 text-xs bg-white/70 px-2 py-0.5 rounded-full">
                    重试 {error.retryCount}
                  </span>
                )}
              </div>
              <div className="text-xs text-gray-600 mt-1">
                {config.description}
              </div>
            </div>

            {/* 错误详情 */}
            <div className="text-xs text-gray-500 bg-white/50 p-2 rounded border">
              <div className="font-medium mb-1">错误详情:</div>
              <div className="break-words">{error.message}</div>
            </div>

            {/* 当前模型信息 */}
            <div className="text-xs text-gray-600 flex items-center gap-1">
              <span>当前模型:</span>
              <span className="font-medium">{getModelDisplayName(currentModel)}</span>
            </div>

            {/* 重试状态 */}
            {showRetrying && (
              <div className="flex items-center gap-2 text-sm text-blue-600">
                <RefreshCw className="w-4 h-4 animate-spin" />
                <span>正在重试...</span>
              </div>
            )}

            {/* 操作按钮 */}
            {!showRetrying && (
              <div className="flex gap-2 pt-1">
                {error.isRetryable && (
                  <Button
                    onClick={handleRetry}
                    disabled={isRetryingLocal}
                    size="sm"
                    variant="outline"
                    className="h-7 px-3 text-xs"
                  >
                    <RefreshCw className="w-3 h-3 mr-1" />
                    重试
                  </Button>
                )}

                {error.canSwitchModel && (
                  <Button
                    onClick={handleSwitchModel}
                    disabled={isSwitching}
                    size="sm"
                    variant="outline"
                    className="h-7 px-3 text-xs"
                  >
                    {isSwitching ? (
                      <>
                        <Zap className="w-3 h-3 mr-1 animate-pulse" />
                        切换中...
                      </>
                    ) : (
                      <>
                        <Zap className="w-3 h-3 mr-1" />
                        切换模型
                      </>
                    )}
                  </Button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
