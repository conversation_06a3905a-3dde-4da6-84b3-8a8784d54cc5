'use client';

import { type Message } from 'ai';
import { useChat } from '@ai-sdk/react';
import { useState } from 'react';
import type { FormEvent, ReactNode } from 'react';
import { toast, Toaster } from 'sonner';
import { StickToBottom, useStickToBottomContext } from 'use-stick-to-bottom';
import { ArrowDown, AlertCircle, RefreshCw } from 'lucide-react';

import { ChatMessageBubble } from '@/components/ChatMessageBubble';
import { ThinkingIndicator } from '@/components/ThinkingIndicator';
import { Button } from '@/components/ui/button';
import { cn } from '@/utils/cn';
import { formatErrorForToast } from '@/utils/error-handler';
import { ChatInput } from '@/components/ChatInput';
import { type ModelConfig } from '@/components/ModelSelector';
import { ErrorMessageBubble } from '@/components/ErrorMessageBubble';

// 模型配置选项
const MODEL_OPTIONS = [
  { type: 'google' as const, label: 'Google Gemini' },
  { type: 'openai' as const, providerType: 'htsc' as const, label: 'HTSC DeepSeek' },
  { type: 'openai' as const, providerType: 'openrouter' as const, label: 'OpenRouter Gemini' },
  { type: 'openai' as const, providerType: 'local' as const, label: 'Local Model' },
];

// 获取下一个可用模型
function getNextAvailableModel(currentConfig: ModelConfig): ModelConfig | null {
  const currentIndex = MODEL_OPTIONS.findIndex(
    opt => opt.type === currentConfig.type &&
    (currentConfig.type === 'google' || opt.providerType === currentConfig.providerType)
  );

  const nextIndex = (currentIndex + 1) % MODEL_OPTIONS.length;
  const nextOption = MODEL_OPTIONS[nextIndex];

  return {
    type: nextOption.type,
    ...(nextOption.providerType && { providerType: nextOption.providerType }),
  };
}

// 获取模型显示名称
function getModelDisplayName(config: ModelConfig): string {
  const option = MODEL_OPTIONS.find(
    opt => opt.type === config.type &&
    (config.type === 'google' || opt.providerType === config.providerType)
  );
  return option?.label || 'Unknown Model';
}

function ChatMessages(props: {
  messages: Message[];
  emptyStateComponent: ReactNode;
  aiEmoji?: string;
  className?: string;
  showThinking?: boolean;
  failedMessageId?: string | null;
  errorMessage?: {
    error: {
      type: 'network' | 'rate_limit' | 'timeout' | 'api_error' | 'unknown';
      message: string;
      isRetryable: boolean;
      canSwitchModel: boolean;
      retryCount?: number;
    };
    currentModel: ModelConfig;
    onRetry: () => void;
    onSwitchModel: () => void;
    isRetrying?: boolean;
  } | null;
}) {
  return (
    <div className="flex flex-col max-w-[800px] mx-auto pb-12 w-full">
      {props.messages.map((m, index) => {
        // 检查是否是失败的消息，如果是则在其后显示错误
        const isFailedMessage = props.failedMessageId === m.id;
        const isLastMessage = index === props.messages.length - 1;
        const shouldShowError = isFailedMessage && props.errorMessage;

        return (
          <div key={m.id}>
            <ChatMessageBubble
              message={m}
              aiEmoji={props.aiEmoji}
              isStreaming={false}
            />

            {/* 在失败的消息后显示错误 */}
            {shouldShowError && props.errorMessage && (
              <ErrorMessageBubble
                error={props.errorMessage.error}
                currentModel={props.errorMessage.currentModel}
                onRetry={props.errorMessage.onRetry}
                onSwitchModel={props.errorMessage.onSwitchModel}
                isRetrying={props.errorMessage.isRetrying}
              />
            )}
          </div>
        );
      })}

      {/* 如果没有失败的消息ID，在最后显示错误（兼容旧逻辑） */}
      {props.errorMessage && !props.failedMessageId && (
        <ErrorMessageBubble
          error={props.errorMessage.error}
          currentModel={props.errorMessage.currentModel}
          onRetry={props.errorMessage.onRetry}
          onSwitchModel={props.errorMessage.onSwitchModel}
          isRetrying={props.errorMessage.isRetrying}
        />
      )}

      {props.showThinking && (
        <div className="flex mt-6">
          <ThinkingIndicator isVisible={true} />
        </div>
      )}
    </div>
  );
}

function ScrollToBottom(props: { className?: string }) {
  const { isAtBottom, scrollToBottom } = useStickToBottomContext();

  if (isAtBottom) return null;
  return (
    <Button variant="outline" className={props.className} onClick={() => scrollToBottom()}>
      <ArrowDown className="w-4 h-4" />
      <span>Scroll to bottom</span>
    </Button>
  );
}



function StickyToBottomContent(props: {
  content: ReactNode;
  footer?: ReactNode;
  className?: string;
  contentClassName?: string;
}) {
  const context = useStickToBottomContext();

  return (
    <div
      ref={context.scrollRef}
      style={{ width: '100%', height: '100%' }}
      className={cn('grid grid-rows-[1fr,auto]', props.className)}
    >
      <div ref={context.contentRef} className={props.contentClassName}>
        {props.content}
      </div>

      {props.footer}
    </div>
  );
}

/**
 * 错误状态组件
 */
function ErrorIndicator(props: {
  onRetry: () => void;
  canRetry: boolean;
  errorMessage: string;
}) {
  return (
    <div className="flex items-center justify-center p-4 bg-red-50 border border-red-200 rounded-lg mx-4 mb-4">
      <div className="flex items-center gap-3">
        <AlertCircle className="h-5 w-5 text-red-500" />
        <div className="flex-1">
          <p className="text-sm text-red-700">{props.errorMessage}</p>
        </div>
        {props.canRetry && (
          <Button
            variant="outline"
            size="sm"
            onClick={props.onRetry}
            className="text-red-600 border-red-300 hover:bg-red-50"
          >
            <RefreshCw className="h-4 w-4 mr-1" />
            重试
          </Button>
        )}
      </div>
    </div>
  );
}

export function ChatWindow(props: {
  endpoint: string;
  emptyStateComponent: ReactNode;
  placeholder?: string;
  emoji?: string;
}) {
  const [showThinking, setShowThinking] = useState(false);
  const [modelConfig, setModelConfig] = useState<ModelConfig>({ type: 'google' });
  const [lastFailedMessage, setLastFailedMessage] = useState<string>('');
  const [isRetrying, setIsRetrying] = useState(false);
  const [failedMessageId, setFailedMessageId] = useState<string | null>(null);
  const [currentError, setCurrentError] = useState<{
    type: 'network' | 'rate_limit' | 'timeout' | 'api_error' | 'unknown';
    message: string;
    isRetryable: boolean;
    canSwitchModel: boolean;
    retryCount?: number;
  } | null>(null);

  const chat = useChat({
    api: props.endpoint,
    body: {
      modelConfig: modelConfig
    },
    onFinish(response: Message) {
      console.log('Final response: ', response?.content);
      setShowThinking(false);
      // 成功时清除错误状态
      setCurrentError(null);
      setFailedMessageId(null);
      setIsRetrying(false);
    },
    onResponse(response: Response) {
      console.log('Response received. Status:', response.status);
      setShowThinking(false);

      // 处理非 200 状态码
      if (!response.ok) {
        response.json().then((errorData) => {
          const errorInfo = formatErrorForToast({ message: JSON.stringify(errorData) });
          toast.error(errorInfo.title, {
            description: errorInfo.description,
            duration: errorInfo.duration
          });
        }).catch(() => {
          toast.error('请求失败', { description: `服务器返回状态码: ${response.status}` });
        });
      }
    },
    onError: (e: Error) => {
      console.error('Chat error: ', e);
      setShowThinking(false);

      const errorInfo = formatErrorForToast(e);

      // 保存失败的消息以便重试
      setLastFailedMessage(chat.input);

      // 记录最后一条用户消息的ID（失败的消息）
      const lastUserMessage = chat.messages.filter(m => m.role === 'user').pop();
      if (lastUserMessage) {
        setFailedMessageId(lastUserMessage.id);
      }

      // 检查是否有错误类型信息
      const errorType = (e as any).errorType || 'unknown';
      const canSwitchModel = (e as any).canSwitchModel !== false;
      const isRetryable = (e as any).isRetryable !== false;
      const retryCount = (e as any).retryCount || 0;

      // 设置当前错误状态
      setCurrentError({
        type: errorType,
        message: e.message,
        isRetryable,
        canSwitchModel,
        retryCount
      });

      let actionButtons = [];

      // 如果可以重试，添加重试按钮
      if (isRetryable) {
        actionButtons.push({
          label: '重试',
          onClick: retryLastMessage
        });
      }

      // 如果可以切换模型，添加切换模型按钮
      if (canSwitchModel) {
        actionButtons.push({
          label: '切换模型重试',
          onClick: switchModelAndRetry
        });
      }

      // 显示错误提示
      toast.error(errorInfo.title, {
        description: errorInfo.description,
        duration: errorInfo.duration,
        action: actionButtons.length > 0 ? {
          label: actionButtons[0].label,
          onClick: actionButtons[0].onClick
        } : undefined
      });

      // 如果有多个操作选项，显示额外的提示
      if (actionButtons.length > 1) {
        setTimeout(() => {
          toast.info('💡 提示', {
            description: '您也可以尝试切换模型后重新发送消息',
            duration: 5000
          });
        }, 1000);
      }
    },
  });

  function isChatLoading(): boolean {
    return chat.status === 'streaming';
  }

  // 重试最后失败的消息
  function retryLastMessage() {
    if (!isChatLoading()) {
      setCurrentError(null);
      setIsRetrying(true);
      setShowThinking(true);
      setFailedMessageId(null);

      // 使用reload重新生成响应，而不是添加新消息
      chat.reload();

      setIsRetrying(false);
    }
  }

  // 切换到下一个模型并重试
  function switchModelAndRetry() {
    const nextModel = getNextAvailableModel(modelConfig);
    if (nextModel) {
      setModelConfig(nextModel);
      setCurrentError(null);

      toast.success(`已切换到 ${getModelDisplayName(nextModel)}`, {
        description: '正在重新发送消息...'
      });

      // 延迟一下再重试，让模型切换生效
      setTimeout(() => {
        retryLastMessage();
      }, 500);
    }
  }

  async function sendMessage(e: FormEvent<HTMLFormElement>) {
    e.preventDefault();
    if (isChatLoading()) return;

    // 检查输入是否为空
    if (!chat.input.trim()) {
      toast.error('请输入消息内容', { description: '不能发送空消息' });
      return;
    }

    // 清除之前的错误状态
    setCurrentError(null);
    setLastFailedMessage('');
    setFailedMessageId(null);

    // 开始思考状态
    setShowThinking(true);

    try {
      chat.handleSubmit(e);
    } catch (error) {
      console.error('发送消息时出错:', error);
      setShowThinking(false);
      toast.error('发送失败', { description: '请检查网络连接后重试' });
    }
  }

  return (
    <>
      <StickToBottom>
        <StickyToBottomContent
          className="absolute inset-0"
          contentClassName="py-8 px-2"
          content={
            chat.messages.length === 0 ? (
              <div>{props.emptyStateComponent}</div>
            ) : (
              <ChatMessages
                aiEmoji={props.emoji}
                messages={chat.messages}
                emptyStateComponent={props.emptyStateComponent}
                showThinking={showThinking}
                failedMessageId={failedMessageId}
                errorMessage={currentError ? {
                  error: currentError,
                  currentModel: modelConfig,
                  onRetry: retryLastMessage,
                  onSwitchModel: switchModelAndRetry,
                  isRetrying: isRetrying
                } : null}
              />
            )
          }
          footer={
            <div className="sticky bottom-4 px-4">
              <ScrollToBottom className="absolute bottom-full left-1/2 -translate-x-1/2 mb-4" />

              <ChatInput
                value={chat.input}
                onChange={chat.handleInputChange}
                onSubmit={sendMessage}
                loading={isChatLoading()}
                placeholder={props.placeholder ?? 'What can I help you with?'}
                modelConfig={modelConfig}
                onModelChange={setModelConfig}
              ></ChatInput>
            </div>
          }
        ></StickyToBottomContent>
      </StickToBottom>
      <Toaster />
    </>
  );
}
