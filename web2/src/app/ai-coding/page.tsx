import { CodingChatWindow } from '@/components/CodingChatWindow';
import { EmptyState } from '@/components/EmptyState';
import { codingEmptyState } from '@/config/emptyStates';

export default async function AICodingPage() {
  return (
    <CodingChatWindow
      endpoint="api/ai-coding"
      emoji="🤖"
      placeholder="请描述您的编程需求，或上传相关文件..."
      emptyStateComponent={<EmptyState {...codingEmptyState} />}
    />
  );
}
