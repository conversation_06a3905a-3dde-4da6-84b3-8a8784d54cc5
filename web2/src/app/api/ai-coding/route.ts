import { NextRequest, NextResponse } from 'next/server';
import { type Message, LangChainAdapter } from 'ai';
import { graph, getDesignToCodeGraph } from "../../../graph/designToCode"
import { DesignItem } from "../../../graph/designToCode/types";
import { v4 as uuidv4 } from "uuid";

import { convertVercelMessageToLangChainMessage } from '@/utils/message-converters';
import { logToolCallsInDevelopment } from '@/utils/stream-logging';
import { extractOrCreateThreadId, buildGraphConfig } from '@/utils/session-manager';

/**
 * This handler initializes and calls the designToCode agent for AI coding tasks.
 * It supports file uploads and converts them to DesignItem format for processing.
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    console.log('AI Coding API: 收到请求', {
      messagesCount: body.messages?.length || 0,
      hasMessages: !!body.messages,
      hasFiles: !!body.files && body.files.length > 0,
      filesCount: body.files?.length || 0,
      hasThreadId: !!body.thread_id
    });

    // 提取或创建thread_id
    const thread_id = await extractOrCreateThreadId(body, 'coding');
    console.log('AI Coding API: 使用会话ID', thread_id);

    /**
     * We represent intermediate steps as system messages for display purposes,
     * but don't want them in the chat history.
     */
    const messages = (body.messages ?? [])
      .filter((message: Message) => message.role === 'user' || message.role === 'assistant')
      .map(convertVercelMessageToLangChainMessage);

    console.log('AI Coding API: 处理后的消息', {
      messagesCount: messages.length,
      lastMessage: messages[messages.length - 1]?.content
    });

    // 获取最后一条用户消息作为输入
    const lastUserMessage = messages
      .filter((msg: any) => msg.getType() === 'human')
      .pop();

    let userInput = (lastUserMessage?.content as string) || '';

    // 处理文件上传，转换为DesignItem格式
    const files = body.files || [];
    const designItems: DesignItem[] = [];

    if (files.length > 0) {
      console.log('AI Coding API: 处理上传的文件', { filesCount: files.length });

      // 将文件转换为DesignItem格式
      files.forEach((file: any) => {
        // 根据文件扩展名判断类型
        const fileExtension = file.name.split('.').pop()?.toLowerCase();
        const isImageFile = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(fileExtension || '');

        designItems.push({
          name: file.name,
          content: file.content,
          type: isImageFile ? "img" : "html" // 将代码文件当作html类型处理
        });
      });

      // 如果没有用户输入，提供默认提示
      if (!userInput.trim()) {
        userInput = `请分析以下上传的文件并提供编程建议或优化方案。`;
      }
    }

    /**
     * Stream back all generated tokens and steps from their runs.
     */
    const graphInput = {
      messages: messages,
      input: designItems,
      output: ''
    };

    // 构建graph配置，包含thread_id用于持久化
    const graphConfig = buildGraphConfig(thread_id);

    console.log('AI Coding API: 调用 designToCode graph', {
      userInput: userInput.substring(0, 200) + (userInput.length > 200 ? '...' : ''),
      messagesCount: messages.length,
      designItemsCount: designItems.length,
      thread_id: thread_id
    });

    try {
      // 获取带持久化支持的graph实例
      const persistentGraph = await getDesignToCodeGraph();

      // 使用 streamEvents 方法，包含thread_id配置
      const eventStream = persistentGraph.streamEvents(graphInput, {
        version: 'v2',
        ...graphConfig
      });

      // 应用流式日志处理器
      const transformedStream = logToolCallsInDevelopment(eventStream);

      // Adapt the LangChain stream to Vercel AI SDK Stream
      return LangChainAdapter.toDataStreamResponse(transformedStream);
    } catch (graphError) {
      console.error('AI Coding API: Graph 执行错误', graphError);
      return NextResponse.json({ error: 'Graph execution failed' }, { status: 500 });
    }
  } catch (e: any) {
    console.error('AI Coding API: 错误', e);
    return NextResponse.json({ error: e.message }, { status: e.status ?? 500 });
  }
}
