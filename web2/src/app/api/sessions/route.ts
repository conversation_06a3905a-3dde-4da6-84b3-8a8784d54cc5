import { NextRequest, NextResponse } from 'next/server';
import {
  createSession,
  createSessionInfo,
  validateSession,
  getSessionHistory,
  getSessionInfo,
  getSessionList,
  getSessionStats,
  updateSessionInfo,
  deleteSession,
  cleanupExpiredSessions,
  getCurrentSessionState,
  getSessionMessages,
  type SessionListParams
} from '@/utils/session-manager';
import { checkMongoHealth } from '@/config/mongodb';

/**
 * 会话管理API
 * GET: 获取会话信息或列表
 * POST: 创建新会话
 * DELETE: 删除会话
 */

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const thread_id = searchParams.get('thread_id');
    const action = searchParams.get('action');

    // 健康检查
    if (action === 'health') {
      const isHealthy = await checkMongoHealth();
      return NextResponse.json({
        status: isHealthy ? 'healthy' : 'unhealthy',
        mongodb: isHealthy
      });
    }

    // 清理过期会话
    if (action === 'cleanup') {
      const maxAgeHours = parseInt(searchParams.get('maxAge') || '24');
      const cleanedCount = await cleanupExpiredSessions(maxAgeHours);
      return NextResponse.json({
        message: `清理了 ${cleanedCount} 个过期会话`,
        cleanedCount
      });
    }

    // 获取会话统计信息
    if (action === 'stats') {
      const user_id = searchParams.get('user_id') || undefined;
      const stats = await getSessionStats(user_id);
      return NextResponse.json(stats);
    }

    // 获取会话列表
    if (action === 'list') {
      const params: SessionListParams = {
        user_id: searchParams.get('user_id') || undefined,
        session_type: (searchParams.get('session_type') as 'chat' | 'coding') || undefined,
        status: (searchParams.get('status') as 'active' | 'archived' | 'deleted') || undefined,
        search: searchParams.get('search') || undefined,
        page: parseInt(searchParams.get('page') || '1'),
        limit: parseInt(searchParams.get('limit') || '20'),
        sort_by: (searchParams.get('sort_by') as 'created_at' | 'last_accessed' | 'message_count') || 'last_accessed',
        sort_order: (searchParams.get('sort_order') as 'asc' | 'desc') || 'desc'
      };

      // 处理标签过滤
      const tagsParam = searchParams.get('tags');
      if (tagsParam) {
        params.tags = tagsParam.split(',').map(tag => tag.trim());
      }

      const result = await getSessionList(params);
      return NextResponse.json(result);
    }

    // 获取特定会话信息
    if (thread_id) {
      const sessionInfo = await getSessionInfo(thread_id);
      if (!sessionInfo) {
        return NextResponse.json(
          { error: '会话不存在' },
          { status: 404 }
        );
      }

      // 检查是否需要详细信息
      const includeState = searchParams.get('include_state') === 'true';
      const includeMessages = searchParams.get('include_messages') === 'true';
      const includeHistory = searchParams.get('include_history') === 'true';

      const messageLimit = parseInt(searchParams.get('message_limit') || '20');
      const historyLimit = parseInt(searchParams.get('history_limit') || '10');

      // 根据需要获取额外信息
      const promises: Promise<any>[] = [];
      let currentState, messages, history;

      if (includeState) {
        promises.push(getCurrentSessionState(thread_id));
      }
      if (includeMessages) {
        promises.push(getSessionMessages(thread_id, messageLimit));
      }
      if (includeHistory) {
        promises.push(getSessionHistory(thread_id, historyLimit));
      }

      if (promises.length > 0) {
        const results = await Promise.all(promises);
        let resultIndex = 0;

        if (includeState) currentState = results[resultIndex++];
        if (includeMessages) messages = results[resultIndex++];
        if (includeHistory) history = results[resultIndex++];
      }

      const response: any = {
        session: sessionInfo,
        timestamp: new Date().toISOString()
      };

      if (includeState) {
        response.current_state = currentState;
        response.has_state = !!currentState;
      }

      if (includeMessages) {
        response.messages = messages || [];
        response.message_count = messages?.length || 0;
      }

      if (includeHistory) {
        response.recent_checkpoints = history || [];
        response.checkpoint_count = history?.length || 0;
      }

      // 如果包含了状态和消息，添加摘要信息
      if (includeState && includeMessages) {
        response.summary = {
          total_messages: messages?.length || 0,
          total_checkpoints: history?.length || 0,
          last_activity: currentState?.timestamp || sessionInfo.last_accessed.toISOString(),
          has_active_state: !!currentState
        };
      }

      return NextResponse.json(response);
    }

    // 如果没有指定thread_id，返回API文档
    return NextResponse.json({
      message: '会话管理API',
      endpoints: {
        'GET ?thread_id=xxx': '获取会话基本信息',
        'GET ?thread_id=xxx&include_state=true': '包含当前状态信息',
        'GET ?thread_id=xxx&include_messages=true': '包含消息历史',
        'GET ?thread_id=xxx&include_history=true': '包含检查点历史',
        'GET ?thread_id=xxx&include_state=true&include_messages=true': '获取完整会话信息',
        'GET ?action=list': '获取会话列表',
        'GET ?action=stats': '获取会话统计信息',
        'GET ?action=health': '健康检查',
        'GET ?action=cleanup': '清理过期会话',
        'POST': '创建新会话',
        'PUT ?thread_id=xxx': '更新会话信息',
        'DELETE ?thread_id=xxx': '删除会话'
      },
      session_detail_params: {
        'include_state': '包含当前LangGraph状态 (true/false)',
        'include_messages': '包含消息历史 (true/false)',
        'include_history': '包含检查点历史 (true/false)',
        'message_limit': '消息数量限制，默认20',
        'history_limit': '历史记录数量限制，默认10'
      },
      list_params: {
        'user_id': '用户ID过滤',
        'session_type': 'chat或coding',
        'status': 'active, archived, deleted',
        'search': '搜索标题和描述',
        'tags': '标签过滤，逗号分隔',
        'page': '页码，默认1',
        'limit': '每页数量，默认20',
        'sort_by': 'created_at, last_accessed, message_count',
        'sort_order': 'asc或desc，默认desc'
      },
      additional_apis: {
        'GET /api/sessions/[thread_id]/state': '获取会话状态详情',
        'GET /api/debug/sessions?thread_id=xxx': '诊断会话问题',
        'POST /api/debug/sessions': '修复会话问题'
      }
    });

  } catch (error) {
    console.error('Sessions API GET: 错误', error);
    return NextResponse.json(
      { error: '获取会话信息失败' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const sessionType = body.sessionType || body.session_type || 'chat';
    const user_id = body.user_id;
    const title = body.title;
    const metadata = body.metadata;

    if (!['chat', 'coding'].includes(sessionType)) {
      return NextResponse.json(
        { error: '无效的会话类型，必须是 chat 或 coding' },
        { status: 400 }
      );
    }

    // 创建完整的会话信息
    const sessionInfo = await createSessionInfo(
      sessionType as 'chat' | 'coding',
      user_id,
      title,
      metadata
    );

    return NextResponse.json({
      message: '会话创建成功',
      session: sessionInfo
    });

  } catch (error) {
    console.error('Sessions API POST: 错误', error);
    return NextResponse.json(
      { error: '创建会话失败' },
      { status: 500 }
    );
  }
}

export async function PUT(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const thread_id = searchParams.get('thread_id');

    if (!thread_id) {
      return NextResponse.json(
        { error: '缺少 thread_id 参数' },
        { status: 400 }
      );
    }

    // 验证会话是否存在
    const existingSession = await getSessionInfo(thread_id);
    if (!existingSession) {
      return NextResponse.json(
        { error: '会话不存在' },
        { status: 404 }
      );
    }

    const body = await req.json();

    // 构建更新数据，只包含允许更新的字段
    const updates: any = {};

    if (body.title !== undefined) updates.title = body.title;
    if (body.description !== undefined) updates.description = body.description;
    if (body.tags !== undefined) updates.tags = body.tags;
    if (body.status !== undefined) {
      if (!['active', 'archived', 'deleted'].includes(body.status)) {
        return NextResponse.json(
          { error: '无效的状态值' },
          { status: 400 }
        );
      }
      updates.status = body.status;
    }
    if (body.is_favorite !== undefined) updates.is_favorite = body.is_favorite;
    if (body.metadata !== undefined) {
      updates.metadata = { ...existingSession.metadata, ...body.metadata };
    }

    // 更新最后访问时间
    updates.last_accessed = new Date();

    await updateSessionInfo(thread_id, updates);

    // 返回更新后的会话信息
    const updatedSession = await getSessionInfo(thread_id);

    return NextResponse.json({
      message: '会话更新成功',
      session: updatedSession
    });

  } catch (error) {
    console.error('Sessions API PUT: 错误', error);
    return NextResponse.json(
      { error: '更新会话失败' },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const thread_id = searchParams.get('thread_id');

    if (!thread_id) {
      return NextResponse.json(
        { error: '缺少 thread_id 参数' },
        { status: 400 }
      );
    }

    // 验证会话是否存在
    const isValid = await validateSession(thread_id);
    if (!isValid) {
      return NextResponse.json(
        { error: '会话不存在' },
        { status: 404 }
      );
    }

    // 删除会话
    const success = await deleteSession(thread_id);
    
    if (success) {
      return NextResponse.json({
        message: '会话删除成功',
        thread_id,
        deleted_at: new Date().toISOString()
      });
    } else {
      return NextResponse.json(
        { error: '删除会话失败' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Sessions API DELETE: 错误', error);
    return NextResponse.json(
      { error: '删除会话失败' },
      { status: 500 }
    );
  }
}
