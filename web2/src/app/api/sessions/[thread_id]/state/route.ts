import { NextRequest, NextResponse } from 'next/server';
import { 
  getCurrentSessionState, 
  getSessionMessages, 
  getSessionHistory,
  validateSession 
} from '@/utils/session-manager';
import { get<PERSON>ongo<PERSON>heckpointer } from '@/config/mongodb';

/**
 * 获取会话状态信息
 * GET /api/sessions/[thread_id]/state
 */
export async function GET(
  req: NextRequest,
  { params }: { params: { thread_id: string } }
) {
  try {
    const { thread_id } = params;
    const { searchParams } = new URL(req.url);
    const action = searchParams.get('action') || 'current';
    const limit = parseInt(searchParams.get('limit') || '50');

    if (!thread_id) {
      return NextResponse.json(
        { error: '缺少 thread_id 参数' },
        { status: 400 }
      );
    }

    // 验证会话是否存在
    const sessionExists = await validateSession(thread_id);
    if (!sessionExists) {
      return NextResponse.json(
        { error: '会话不存在或已过期' },
        { status: 404 }
      );
    }

    switch (action) {
      case 'current':
        // 获取当前状态
        const currentState = await getCurrentSessionState(thread_id);
        return NextResponse.json({
          thread_id,
          current_state: currentState,
          timestamp: new Date().toISOString()
        });

      case 'messages':
        // 获取消息历史
        const messages = await getSessionMessages(thread_id, limit);
        return NextResponse.json({
          thread_id,
          messages,
          total_messages: messages.length,
          timestamp: new Date().toISOString()
        });

      case 'history':
        // 获取检查点历史
        const history = await getSessionHistory(thread_id, limit);
        return NextResponse.json({
          thread_id,
          checkpoints: history,
          total_checkpoints: history.length,
          timestamp: new Date().toISOString()
        });

      case 'full':
        // 获取完整状态信息
        const [fullState, fullMessages, fullHistory] = await Promise.all([
          getCurrentSessionState(thread_id),
          getSessionMessages(thread_id, limit),
          getSessionHistory(thread_id, Math.min(limit, 10))
        ]);

        return NextResponse.json({
          thread_id,
          current_state: fullState,
          messages: fullMessages,
          recent_checkpoints: fullHistory,
          summary: {
            total_messages: fullMessages.length,
            total_checkpoints: fullHistory.length,
            last_activity: fullState?.timestamp || null
          },
          timestamp: new Date().toISOString()
        });

      default:
        return NextResponse.json(
          { error: `不支持的操作: ${action}` },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('获取会话状态失败:', error);
    return NextResponse.json(
      { 
        error: '获取会话状态失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

/**
 * 更新会话状态（高级操作）
 * PUT /api/sessions/[thread_id]/state
 */
export async function PUT(
  req: NextRequest,
  { params }: { params: { thread_id: string } }
) {
  try {
    const { thread_id } = params;
    const body = await req.json();
    const { action, checkpoint_id } = body;

    if (!thread_id) {
      return NextResponse.json(
        { error: '缺少 thread_id 参数' },
        { status: 400 }
      );
    }

    // 验证会话是否存在
    const sessionExists = await validateSession(thread_id);
    if (!sessionExists) {
      return NextResponse.json(
        { error: '会话不存在或已过期' },
        { status: 404 }
      );
    }

    const checkpointer = await getMongoCheckpointer();
    const config = { configurable: { thread_id } };

    switch (action) {
      case 'revert':
        // 回滚到指定检查点
        if (!checkpoint_id) {
          return NextResponse.json(
            { error: '缺少 checkpoint_id 参数' },
            { status: 400 }
          );
        }

        // 这里需要实现回滚逻辑
        // 注意：LangGraph的checkpointer可能不直接支持回滚
        // 这是一个高级功能，需要谨慎实现
        return NextResponse.json(
          { 
            message: '回滚功能暂未实现',
            note: '这需要自定义实现，因为LangGraph checkpointer不直接支持状态回滚'
          },
          { status: 501 }
        );

      case 'clear':
        // 清除会话状态（保留会话信息但清除检查点）
        try {
          // 注意：这会删除所有检查点数据
          await checkpointer.deleteThread(thread_id);
          
          return NextResponse.json({
            message: '会话状态已清除',
            thread_id,
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          console.error('清除会话状态失败:', error);
          return NextResponse.json(
            { error: '清除会话状态失败' },
            { status: 500 }
          );
        }

      default:
        return NextResponse.json(
          { error: `不支持的操作: ${action}` },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('更新会话状态失败:', error);
    return NextResponse.json(
      { 
        error: '更新会话状态失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
