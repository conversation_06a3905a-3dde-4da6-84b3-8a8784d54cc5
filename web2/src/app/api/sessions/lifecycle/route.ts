import { NextRequest, NextResponse } from 'next/server';
import { 
  runLifecycleMaintenance,
  autoArchiveSessions,
  autoDeleteSessions,
  cleanupExpiredBackups,
  createSessionBackup,
  restoreSessionFromBackup,
  enforceUserSessionLimit
} from '@/utils/session-lifecycle';

/**
 * 会话生命周期管理API
 * GET: 执行生命周期维护任务
 * POST: 创建备份或恢复会话
 */

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const action = searchParams.get('action');
    const userId = searchParams.get('user_id');

    switch (action) {
      case 'maintenance':
        // 执行完整的生命周期维护
        const config = {
          autoArchiveAfterDays: parseInt(searchParams.get('archiveDays') || '30'),
          autoDeleteAfterDays: parseInt(searchParams.get('deleteDays') || '90'),
          backupRetentionDays: parseInt(searchParams.get('backupDays') || '365'),
          maxSessionsPerUser: parseInt(searchParams.get('maxSessions') || '100')
        };
        
        const maintenanceResult = await runLifecycleMaintenance(config);
        
        return NextResponse.json({
          message: '生命周期维护完成',
          ...maintenanceResult
        });

      case 'archive':
        // 手动归档过期会话
        const archiveDays = parseInt(searchParams.get('days') || '30');
        const archivedCount = await autoArchiveSessions({ autoArchiveAfterDays: archiveDays });
        
        return NextResponse.json({
          message: `已归档 ${archivedCount} 个会话`,
          archivedCount
        });

      case 'delete':
        // 手动删除过期会话
        const deleteDays = parseInt(searchParams.get('days') || '90');
        const deletedCount = await autoDeleteSessions({ autoDeleteAfterDays: deleteDays });
        
        return NextResponse.json({
          message: `已删除 ${deletedCount} 个会话`,
          deletedCount
        });

      case 'cleanup-backups':
        // 清理过期备份
        const backupDays = parseInt(searchParams.get('days') || '365');
        const cleanedCount = await cleanupExpiredBackups({ backupRetentionDays: backupDays });
        
        return NextResponse.json({
          message: `已清理 ${cleanedCount} 个过期备份`,
          cleanedCount
        });

      case 'enforce-limit':
        // 限制用户会话数量
        if (!userId) {
          return NextResponse.json(
            { error: '缺少 user_id 参数' },
            { status: 400 }
          );
        }
        
        const maxSessions = parseInt(searchParams.get('maxSessions') || '100');
        const limitedCount = await enforceUserSessionLimit(userId, { maxSessionsPerUser: maxSessions });
        
        return NextResponse.json({
          message: `用户 ${userId} 已限制会话数量，归档了 ${limitedCount} 个会话`,
          limitedCount
        });

      default:
        return NextResponse.json({
          message: '会话生命周期管理API',
          endpoints: {
            'GET ?action=maintenance': '执行完整的生命周期维护',
            'GET ?action=archive&days=30': '归档指定天数前的会话',
            'GET ?action=delete&days=90': '删除指定天数前的会话',
            'GET ?action=cleanup-backups&days=365': '清理指定天数前的备份',
            'GET ?action=enforce-limit&user_id=xxx&maxSessions=100': '限制用户会话数量',
            'POST': '创建备份或恢复会话'
          },
          maintenance_params: {
            'archiveDays': '归档天数，默认30',
            'deleteDays': '删除天数，默认90',
            'backupDays': '备份保留天数，默认365',
            'maxSessions': '每用户最大会话数，默认100'
          }
        });
    }
  } catch (error) {
    console.error('SessionLifecycle API GET: 错误', error);
    return NextResponse.json(
      { error: '生命周期管理操作失败' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { action, thread_id, backup_type } = body;

    if (!thread_id) {
      return NextResponse.json(
        { error: '缺少 thread_id 参数' },
        { status: 400 }
      );
    }

    switch (action) {
      case 'backup':
        // 创建会话备份
        const backupSuccess = await createSessionBackup(
          thread_id, 
          backup_type || 'manual'
        );
        
        if (backupSuccess) {
          return NextResponse.json({
            message: '会话备份创建成功',
            thread_id,
            backup_type: backup_type || 'manual',
            created_at: new Date().toISOString()
          });
        } else {
          return NextResponse.json(
            { error: '创建会话备份失败' },
            { status: 500 }
          );
        }

      case 'restore':
        // 恢复会话从备份
        const restoreSuccess = await restoreSessionFromBackup(thread_id);
        
        if (restoreSuccess) {
          return NextResponse.json({
            message: '会话恢复成功',
            thread_id,
            restored_at: new Date().toISOString()
          });
        } else {
          return NextResponse.json(
            { error: '恢复会话失败，可能没有找到备份' },
            { status: 404 }
          );
        }

      default:
        return NextResponse.json(
          { error: '无效的操作类型，支持: backup, restore' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('SessionLifecycle API POST: 错误', error);
    return NextResponse.json(
      { error: '生命周期管理操作失败' },
      { status: 500 }
    );
  }
}

// 可选：添加定时任务触发器（需要配合cron job或其他调度系统）
export async function PUT(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const schedule = searchParams.get('schedule'); // daily, weekly, monthly
    
    // 这里可以设置不同的维护计划
    let config = {};
    
    switch (schedule) {
      case 'daily':
        config = {
          autoArchiveAfterDays: 7,
          autoDeleteAfterDays: 30,
          backupRetentionDays: 90,
          maxSessionsPerUser: 50
        };
        break;
      case 'weekly':
        config = {
          autoArchiveAfterDays: 30,
          autoDeleteAfterDays: 90,
          backupRetentionDays: 365,
          maxSessionsPerUser: 100
        };
        break;
      case 'monthly':
        config = {
          autoArchiveAfterDays: 90,
          autoDeleteAfterDays: 365,
          backupRetentionDays: 1095, // 3年
          maxSessionsPerUser: 200
        };
        break;
      default:
        return NextResponse.json(
          { error: '无效的调度类型，支持: daily, weekly, monthly' },
          { status: 400 }
        );
    }
    
    const result = await runLifecycleMaintenance(config);
    
    return NextResponse.json({
      message: `${schedule} 维护任务完成`,
      schedule,
      ...result,
      executed_at: new Date().toISOString()
    });
  } catch (error) {
    console.error('SessionLifecycle API PUT: 错误', error);
    return NextResponse.json(
      { error: '定时维护任务失败' },
      { status: 500 }
    );
  }
}
