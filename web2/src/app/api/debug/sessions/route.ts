import { NextRequest, NextResponse } from 'next/server';
import { 
  diagnoseSession, 
  repairSession, 
  getSystemDiagnostics 
} from '@/utils/session-debug';

/**
 * 会话调试API
 * GET /api/debug/sessions - 获取系统诊断信息
 * GET /api/debug/sessions?thread_id=xxx - 诊断特定会话
 */
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const thread_id = searchParams.get('thread_id');
    const action = searchParams.get('action') || 'diagnose';

    if (!thread_id) {
      // 返回系统级别的诊断信息
      const systemDiagnostics = await getSystemDiagnostics();
      return NextResponse.json({
        type: 'system_diagnostics',
        data: systemDiagnostics,
        timestamp: new Date().toISOString()
      });
    }

    if (action === 'diagnose') {
      // 诊断特定会话
      const diagnostics = await diagnoseSession(thread_id);
      return NextResponse.json({
        type: 'session_diagnostics',
        data: diagnostics,
        timestamp: new Date().toISOString()
      });
    }

    return NextResponse.json(
      { error: `不支持的操作: ${action}` },
      { status: 400 }
    );

  } catch (error) {
    console.error('调试API失败:', error);
    return NextResponse.json(
      { 
        error: '调试操作失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

/**
 * 会话修复API
 * POST /api/debug/sessions
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { thread_id, action, options = {} } = body;

    if (!thread_id) {
      return NextResponse.json(
        { error: '缺少 thread_id 参数' },
        { status: 400 }
      );
    }

    if (action === 'repair') {
      const result = await repairSession(thread_id, options);
      return NextResponse.json({
        type: 'repair_result',
        thread_id,
        result,
        timestamp: new Date().toISOString()
      });
    }

    return NextResponse.json(
      { error: `不支持的操作: ${action}` },
      { status: 400 }
    );

  } catch (error) {
    console.error('修复API失败:', error);
    return NextResponse.json(
      { 
        error: '修复操作失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
