import './globals.css';
// import { Roboto_Mono, Inter } from 'next/font/google';
import { Navbar } from '@/components/Navbar';

// const publicSans = Inter({ weight: '400', subsets: ['latin'] });

export default async function RootLayout({ children }: { children: React.ReactNode }) {

  return (
    <html lang="en">
      {/* className={publicSans.className} */}
      <body suppressHydrationWarning>
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50 grid grid-rows-[auto,1fr]">
          <Navbar title="LangGraph MVP" />
          <div className="relative overflow-hidden">
            {/* 背景装饰 */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-purple-50/30 to-pink-50/50"></div>
            <div className="absolute top-0 left-1/4 w-96 h-96 bg-blue-200/20 rounded-full blur-3xl animate-float"></div>
            <div className="absolute bottom-0 right-1/4 w-80 h-80 bg-purple-200/20 rounded-full blur-3xl animate-float" style={{animationDelay: '2s'}}></div>
            <div className="absolute top-1/2 left-1/2 w-64 h-64 bg-pink-200/20 rounded-full blur-3xl animate-float" style={{animationDelay: '4s'}}></div>

            <div className="relative z-10 h-full">
              {children}
            </div>
          </div>
        </div>
      </body>
    </html>
  );
}
