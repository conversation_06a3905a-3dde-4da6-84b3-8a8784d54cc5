/**
 * 错误处理工具类的测试
 */

import { analyzeError, ErrorType, formatErrorForToast, isRetryableError, getRetryDelay } from './error-handler';

describe('Error Handler', () => {
  describe('analyzeError', () => {
    it('should identify network errors correctly', () => {
      const error = { status: 502, message: 'Bad Gateway' };
      const result = analyzeError(error);
      
      expect(result.type).toBe(ErrorType.NETWORK_ERROR);
      expect(result.isRetryable).toBe(true);
      expect(result.title).toBe('网络连接问题');
    });

    it('should identify rate limit errors correctly', () => {
      const error = { status: 429, message: 'Rate limit exceeded' };
      const result = analyzeError(error);
      
      expect(result.type).toBe(ErrorType.RATE_LIMIT_ERROR);
      expect(result.isRetryable).toBe(true);
      expect(result.retryAfter).toBe(60);
    });

    it('should identify timeout errors correctly', () => {
      const error = { message: 'Request timeout' };
      const result = analyzeError(error);
      
      expect(result.type).toBe(ErrorType.TIMEOUT_ERROR);
      expect(result.isRetryable).toBe(true);
    });

    it('should identify API errors correctly', () => {
      const error = { status: 400, message: 'Bad request' };
      const result = analyzeError(error);
      
      expect(result.type).toBe(ErrorType.API_ERROR);
      expect(result.isRetryable).toBe(false);
    });

    it('should handle JSON error responses', () => {
      const error = { 
        message: '{"error": "Service unavailable", "type": "SERVICE_UNAVAILABLE", "retryAfter": 30}' 
      };
      const result = analyzeError(error);
      
      expect(result.type).toBe(ErrorType.SERVICE_UNAVAILABLE);
      expect(result.isRetryable).toBe(true);
      expect(result.retryAfter).toBe(30);
    });
  });

  describe('formatErrorForToast', () => {
    it('should format error for toast notification', () => {
      const error = { status: 502, message: 'Bad Gateway' };
      const result = formatErrorForToast(error);
      
      expect(result.title).toBe('网络连接问题');
      expect(result.description).toBe('服务器暂时无法响应，请稍后再试。');
      expect(result.duration).toBe(30000);
    });
  });

  describe('isRetryableError', () => {
    it('should return true for retryable errors', () => {
      const error = { status: 502 };
      expect(isRetryableError(error)).toBe(true);
    });

    it('should return false for non-retryable errors', () => {
      const error = { status: 400 };
      expect(isRetryableError(error)).toBe(false);
    });
  });

  describe('getRetryDelay', () => {
    it('should return correct retry delay', () => {
      const error = { status: 429 };
      expect(getRetryDelay(error)).toBe(60);
    });

    it('should return default delay for unknown errors', () => {
      const error = { status: 500 };
      expect(getRetryDelay(error)).toBe(30);
    });
  });
});
