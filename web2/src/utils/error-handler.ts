/**
 * 错误处理工具类
 * 提供统一的错误分析、处理和用户友好的错误消息生成
 */

export enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  API_ERROR = 'API_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  INVALID_INPUT = 'INVALID_INPUT',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

export interface ErrorInfo {
  type: ErrorType;
  title: string;
  description: string;
  isRetryable: boolean;
  retryAfter?: number;
  suggestions?: string[];
}

export interface ErrorResponse {
  error: string;
  type?: string;
  retryAfter?: number;
}

/**
 * 分析错误并返回详细的错误信息
 */
export function analyzeError(error: any): ErrorInfo {
  const errorMessage = error?.message || '';
  const status = error?.status || error?.statusCode;

  // 尝试解析 JSON 错误响应
  let errorData: ErrorResponse | null = null;
  try {
    if (typeof errorMessage === 'string') {
      const jsonMatch = errorMessage.match(/\{.*\}/);
      if (jsonMatch) {
        errorData = JSON.parse(jsonMatch[0]);
      }
    }
  } catch (parseError) {
    // 忽略解析错误
  }

  // 根据错误类型或状态码分析
  if (errorData?.type) {
    switch (errorData.type) {
      case 'SERVICE_UNAVAILABLE':
        return {
          type: ErrorType.SERVICE_UNAVAILABLE,
          title: '服务暂时不可用',
          description: '我们的AI服务正在维护中，请稍后再试。',
          isRetryable: true,
          retryAfter: errorData.retryAfter || 30,
          suggestions: [
            '等待几分钟后重试',
            '检查网络连接',
            '如果问题持续，请联系技术支持'
          ]
        };
      case 'RATE_LIMIT':
        return {
          type: ErrorType.RATE_LIMIT_ERROR,
          title: '请求过于频繁',
          description: '为了保证服务质量，请稍等片刻再发送消息。',
          isRetryable: true,
          retryAfter: errorData.retryAfter || 60,
          suggestions: [
            '等待一分钟后重试',
            '避免连续快速发送消息',
            '尝试合并多个问题为一条消息'
          ]
        };
      case 'INVALID_INPUT':
        return {
          type: ErrorType.INVALID_INPUT,
          title: '输入内容有误',
          description: '请检查您的输入内容并重新发送。',
          isRetryable: false,
          suggestions: [
            '确保消息内容不为空',
            '检查特殊字符或格式',
            '尝试重新组织您的问题'
          ]
        };
      case 'CLIENT_ERROR':
        return {
          type: ErrorType.API_ERROR,
          title: '请求格式错误',
          description: '请重新组织您的问题并再次尝试。',
          isRetryable: false,
          suggestions: [
            '简化您的问题',
            '避免使用特殊字符',
            '确保消息格式正确'
          ]
        };
    }
  }

  // 网络错误 (502, 503, 504)
  if (status === 502 || status === 503 || status === 504 || 
      errorMessage.includes('Bad Gateway') || 
      errorMessage.includes('Service Unavailable') ||
      errorMessage.includes('Gateway Timeout')) {
    return {
      type: ErrorType.NETWORK_ERROR,
      title: '网络连接问题',
      description: '服务器暂时无法响应，请稍后再试。',
      isRetryable: true,
      retryAfter: 30,
      suggestions: [
        '检查网络连接',
        '等待30秒后重试',
        '尝试刷新页面'
      ]
    };
  }

  // 速率限制错误 (429)
  if (status === 429 || errorMessage.includes('rate limit') || errorMessage.includes('quota')) {
    return {
      type: ErrorType.RATE_LIMIT_ERROR,
      title: '请求过于频繁',
      description: '请求次数超过限制，请稍后再试。',
      isRetryable: true,
      retryAfter: 60,
      suggestions: [
        '等待一分钟后重试',
        '减少请求频率',
        '避免重复发送相同消息'
      ]
    };
  }

  // 超时错误
  if (errorMessage.includes('timeout') || errorMessage.includes('TIMEOUT')) {
    return {
      type: ErrorType.TIMEOUT_ERROR,
      title: '请求超时',
      description: '服务器响应时间过长，请重试。',
      isRetryable: true,
      retryAfter: 10,
      suggestions: [
        '尝试发送较短的消息',
        '检查网络连接',
        '稍后重试'
      ]
    };
  }

  // API 错误 (4xx)
  if (status >= 400 && status < 500) {
    return {
      type: ErrorType.API_ERROR,
      title: '请求错误',
      description: '请求格式有误，请重新输入您的问题。',
      isRetryable: false,
      suggestions: [
        '检查输入内容',
        '重新组织您的问题',
        '避免使用特殊字符'
      ]
    };
  }

  // 网络连接错误
  if (errorMessage.includes('fetch') || errorMessage.includes('network') || errorMessage.includes('Failed to fetch')) {
    return {
      type: ErrorType.NETWORK_ERROR,
      title: '网络连接错误',
      description: '无法连接到服务器，请检查网络连接。',
      isRetryable: true,
      retryAfter: 10,
      suggestions: [
        '检查网络连接',
        '尝试刷新页面',
        '稍后重试'
      ]
    };
  }

  // 未知错误
  return {
    type: ErrorType.UNKNOWN_ERROR,
    title: '出现了问题',
    description: errorData?.error || errorMessage || '遇到了一些技术问题，请稍后再试。',
    isRetryable: true,
    retryAfter: 30,
    suggestions: [
      '稍后重试',
      '刷新页面',
      '如果问题持续，请联系技术支持'
    ]
  };
}

/**
 * 生成用户友好的错误消息
 */
export function generateUserFriendlyMessage(error: any): string {
  const errorInfo = analyzeError(error);
  
  let message = errorInfo.description;
  
  if (errorInfo.suggestions && errorInfo.suggestions.length > 0) {
    message += '\n\n💡 建议：\n' + errorInfo.suggestions.map(s => `• ${s}`).join('\n');
  }
  
  return message;
}

/**
 * 检查错误是否可以重试
 */
export function isRetryableError(error: any): boolean {
  const errorInfo = analyzeError(error);
  return errorInfo.isRetryable;
}

/**
 * 获取重试延迟时间（秒）
 */
export function getRetryDelay(error: any): number {
  const errorInfo = analyzeError(error);
  return errorInfo.retryAfter || 30;
}

/**
 * 为 Toast 通知格式化错误信息
 */
export function formatErrorForToast(error: any): { title: string; description: string; duration?: number } {
  const errorInfo = analyzeError(error);
  
  return {
    title: errorInfo.title,
    description: errorInfo.description,
    duration: errorInfo.retryAfter ? errorInfo.retryAfter * 1000 : 5000
  };
}
