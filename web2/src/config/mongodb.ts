import { MongoDBSaver } from "@langchain/langgraph-checkpoint-mongodb";
import { MongoClient } from "mongodb";

// MongoDB配置
const MONGODB_URI = process.env.MONGODB_URI || "****************************************************";
const MONGODB_DB_NAME = process.env.MONGODB_DB_NAME || "haicode_cli";

// MongoDB客户端实例
let mongoClient: MongoClient | null = null;
let mongoCheckpointer: MongoDBSaver | null = null;

/**
 * 获取MongoDB客户端实例
 */
export async function getMongoClient(): Promise<MongoClient> {
  if (!mongoClient) {
    console.log("MongoDB: 创建新的客户端连接...");
    mongoClient = new MongoClient(MONGODB_URI);
    
    try {
      await mongoClient.connect();
      console.log("MongoDB: 连接成功");
      
      // 测试连接
      await mongoClient.db(MONGODB_DB_NAME).admin().ping();
      console.log("MongoDB: 连接测试通过");
    } catch (error) {
      console.error("MongoDB: 连接失败", error);
      mongoClient = null;
      throw error;
    }
  }
  
  return mongoClient;
}

/**
 * 获取MongoDB Checkpointer实例
 */
export async function getMongoCheckpointer(): Promise<MongoDBSaver> {
  if (!mongoCheckpointer) {
    console.log("MongoDB: 创建新的Checkpointer实例...");
    
    try {
      const client = await getMongoClient();
      
      // 创建checkpointer实例，使用正确的参数
      mongoCheckpointer = new MongoDBSaver({
        client,
        dbName: MONGODB_DB_NAME,
        checkpointCollectionName: "langgraph_checkpoints", // 检查点集合名称
        checkpointWritesCollectionName: "langgraph_checkpoint_writes" // 检查点写入集合名称
      });
      
      console.log("MongoDB: Checkpointer创建完成");
      
    } catch (error) {
      console.error("MongoDB: Checkpointer创建失败", error);
      throw error;
    }
  }
  
  return mongoCheckpointer;
}

/**
 * 关闭MongoDB连接
 */
export async function closeMongoConnection(): Promise<void> {
  if (mongoClient) {
    console.log("MongoDB: 关闭连接...");
    await mongoClient.close();
    mongoClient = null;
    mongoCheckpointer = null;
    console.log("MongoDB: 连接已关闭");
  }
}

/**
 * 健康检查
 */
export async function checkMongoHealth(): Promise<boolean> {
  try {
    const client = await getMongoClient();
    await client.db(MONGODB_DB_NAME).admin().ping();
    return true;
  } catch (error) {
    console.error("MongoDB: 健康检查失败", error);
    return false;
  }
}

// 进程退出时清理连接
process.on('SIGINT', async () => {
  await closeMongoConnection();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await closeMongoConnection();
  process.exit(0);
});
