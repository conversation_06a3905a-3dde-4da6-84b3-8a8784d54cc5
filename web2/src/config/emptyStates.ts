export const chatEmptyState = {
  emoji: "💬",
  title: "智能聊天助手",
  description: "我是您的AI助手，可以回答各种问题、提供建议、帮助解决问题",
  features: [
    {
      icon: "🎯",
      title: "智能问答",
      description: "快速准确的回答您的问题",
      gradient: "from-blue-600 to-blue-700",
      borderColor: "border-blue-200",
      textColor: "text-blue-800",
      bgGradient: "bg-gradient-to-br from-blue-50 to-blue-100"
    },
    {
      icon: "💡",
      title: "创意建议",
      description: "提供创新的想法和解决方案",
      gradient: "from-purple-600 to-purple-700",
      borderColor: "border-purple-200",
      textColor: "text-purple-800",
      bgGradient: "bg-gradient-to-br from-purple-50 to-purple-100"
    },
    {
      icon: "🔍",
      title: "深度分析",
      description: "深入分析复杂问题",
      gradient: "from-green-600 to-green-700",
      borderColor: "border-green-200",
      textColor: "text-green-800",
      bgGradient: "bg-gradient-to-br from-green-50 to-green-100"
    },
    {
      icon: "🚀",
      title: "高效协作",
      description: "提升您的工作效率",
      gradient: "from-orange-600 to-orange-700",
      borderColor: "border-orange-200",
      textColor: "text-orange-800",
      bgGradient: "bg-gradient-to-br from-orange-50 to-orange-100"
    }
  ],
  tips: [
    "💡 试试问一些有趣的问题",
    "🎨 或者让我帮您解决实际问题"
  ]
};

export const codingEmptyState = {
  emoji: "🤖",
  title: "AI 编程助手",
  // 同步蓝湖设计稿 • 创建页面 • 绑定原型 • 生成代码
  description: "我可以帮助您编写代码、调试问题、解释代码逻辑等编程相关任务",
  features: [
    {
      icon: "💻",
      title: "代码生成",
      description: "根据需求自动生成代码",
      gradient: "from-blue-600 to-blue-700",
      borderColor: "border-blue-200",
      textColor: "text-blue-800",
      bgGradient: "bg-gradient-to-br from-blue-50 to-blue-100"
    },
    {
      icon: "🐛",
      title: "调试优化",
      description: "快速定位和修复问题",
      gradient: "from-green-600 to-green-700",
      borderColor: "border-green-200",
      textColor: "text-green-800",
      bgGradient: "bg-gradient-to-br from-green-50 to-green-100"
    },
    {
      icon: "📚",
      title: "代码解释",
      description: "详细解释代码逻辑",
      gradient: "from-purple-600 to-purple-700",
      borderColor: "border-purple-200",
      textColor: "text-purple-800",
      bgGradient: "bg-gradient-to-br from-purple-50 to-purple-100"
    },
    {
      icon: "⚡",
      title: "性能优化",
      description: "提升代码执行效率",
      gradient: "from-orange-600 to-orange-700",
      borderColor: "border-orange-200",
      textColor: "text-orange-800",
      bgGradient: "bg-gradient-to-br from-orange-50 to-orange-100"
    }
  ],
  // capabilities: [
  //   "多种编程语言",
  //   "文件上传分析",
  //   "代码优化建议",
  //   "编程问题解答"
  // ],
  tips: [
    "💡 试试上传一个代码文件让我分析",
    "🚀 或者描述您的编程需求"
  ]
};
