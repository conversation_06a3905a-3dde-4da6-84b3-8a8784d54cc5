/**
 * 会话状态API测试
 * 验证新增的会话状态查询功能
 * 
 * 运行测试：
 * npm test -- session-state-api.test.ts
 */

import { 
  getCurrentSessionState, 
  getSessionMessages, 
  getSessionHistory 
} from '../utils/session-manager';
import { diagnoseSession, repairSession } from '../utils/session-debug';
import { getSimpleChatGraph } from '../graph/simpleChat';

// 测试配置
const TEST_CONFIG = {
  timeout: 30000,
  testThreadId: 'test-session-' + Date.now(),
  testMessages: [
    "你好，这是测试消息1",
    "请告诉我LangGraph的工作原理",
    "谢谢你的详细解释"
  ]
};

describe('会话状态API测试', () => {
  let testThreadId: string;
  let graph: any;

  beforeAll(async () => {
    testThreadId = TEST_CONFIG.testThreadId;
    graph = await getSimpleChatGraph();
    console.log(`开始测试，使用thread_id: ${testThreadId}`);
  }, TEST_CONFIG.timeout);

  afterAll(async () => {
    // 清理测试数据
    try {
      await repairSession(testThreadId, { 
        clearCheckpoints: true,
        recreateSession: false 
      });
      console.log('测试数据清理完成');
    } catch (error) {
      console.warn('清理测试数据失败:', error);
    }
  });

  describe('基础会话状态功能', () => {
    test('创建测试会话并生成状态', async () => {
      const config = { configurable: { thread_id: testThreadId } };
      
      // 发送测试消息
      for (const message of TEST_CONFIG.testMessages) {
        const result = await graph.invoke({ input: message }, config);
        expect(result).toBeDefined();
        expect(result.output).toBeDefined();
        
        console.log(`消息处理完成: ${message.substring(0, 20)}...`);
      }
    }, TEST_CONFIG.timeout);

    test('获取当前会话状态', async () => {
      const currentState = await getCurrentSessionState(testThreadId);
      
      expect(currentState).toBeDefined();
      expect(currentState?.thread_id).toBe(testThreadId);
      expect(currentState?.state).toBeDefined();
      expect(currentState?.checkpoint_id).toBeDefined();
      
      console.log('当前状态检查通过:', {
        hasState: !!currentState,
        stateKeys: Object.keys(currentState?.state || {}),
        checkpointId: currentState?.checkpoint_id?.substring(0, 8) + '...'
      });
    });

    test('获取会话消息历史', async () => {
      const messages = await getSessionMessages(testThreadId, 10);
      
      expect(Array.isArray(messages)).toBe(true);
      expect(messages.length).toBeGreaterThan(0);
      
      // 验证消息结构
      const firstMessage = messages[0];
      expect(firstMessage).toHaveProperty('content');
      expect(firstMessage).toHaveProperty('role');
      expect(firstMessage).toHaveProperty('type');
      
      console.log('消息历史检查通过:', {
        messageCount: messages.length,
        firstMessageRole: firstMessage.role,
        firstMessageType: firstMessage.type
      });
    });

    test('获取检查点历史', async () => {
      const history = await getSessionHistory(testThreadId, 5);
      
      expect(Array.isArray(history)).toBe(true);
      expect(history.length).toBeGreaterThan(0);
      
      // 验证检查点结构
      const firstCheckpoint = history[0];
      expect(firstCheckpoint).toHaveProperty('checkpoint_id');
      expect(firstCheckpoint).toHaveProperty('timestamp');
      expect(firstCheckpoint).toHaveProperty('state');
      
      console.log('检查点历史检查通过:', {
        checkpointCount: history.length,
        hasState: !!firstCheckpoint.state,
        stateKeys: Object.keys(firstCheckpoint.state || {})
      });
    });
  });

  describe('诊断功能测试', () => {
    test('诊断会话状态', async () => {
      const diagnostics = await diagnoseSession(testThreadId);
      
      expect(diagnostics).toBeDefined();
      expect(diagnostics.thread_id).toBe(testThreadId);
      expect(diagnostics.session_exists).toBe(true);
      expect(diagnostics.checkpoint_exists).toBe(true);
      expect(diagnostics.checkpointer_health).toBe(true);
      expect(Array.isArray(diagnostics.recommendations)).toBe(true);
      
      console.log('诊断结果:', {
        sessionExists: diagnostics.session_exists,
        checkpointExists: diagnostics.checkpoint_exists,
        messageCount: diagnostics.message_count,
        recommendations: diagnostics.recommendations.length
      });
    });

    test('验证状态完整性', async () => {
      const diagnostics = await diagnoseSession(testThreadId);
      
      // 验证状态摘要
      expect(diagnostics.state_summary.has_messages).toBe(true);
      expect(diagnostics.state_summary.state_keys.length).toBeGreaterThan(0);
      expect(diagnostics.state_summary.state_keys).toContain('messages');
      
      // 验证MongoDB集合状态
      expect(diagnostics.mongodb_collections.sessions).toBeGreaterThan(0);
      expect(diagnostics.mongodb_collections.checkpoints).toBeGreaterThan(0);
      
      console.log('状态完整性验证通过:', diagnostics.state_summary);
    });
  });

  describe('API端点测试', () => {
    test('测试会话详情API', async () => {
      const response = await fetch(
        `http://localhost:3000/api/sessions?thread_id=${testThreadId}&include_state=true&include_messages=true&include_history=true`
      );
      
      expect(response.ok).toBe(true);
      
      const data = await response.json();
      expect(data.session).toBeDefined();
      expect(data.current_state).toBeDefined();
      expect(data.messages).toBeDefined();
      expect(data.recent_checkpoints).toBeDefined();
      expect(data.summary).toBeDefined();
      
      console.log('会话详情API测试通过:', {
        hasSession: !!data.session,
        hasState: !!data.current_state,
        messageCount: data.messages?.length || 0,
        checkpointCount: data.recent_checkpoints?.length || 0
      });
    });

    test('测试状态查询API', async () => {
      const response = await fetch(
        `http://localhost:3000/api/sessions/${testThreadId}/state?action=full`
      );
      
      expect(response.ok).toBe(true);
      
      const data = await response.json();
      expect(data.thread_id).toBe(testThreadId);
      expect(data.current_state).toBeDefined();
      expect(data.messages).toBeDefined();
      expect(data.summary).toBeDefined();
      
      console.log('状态查询API测试通过');
    });

    test('测试诊断API', async () => {
      const response = await fetch(
        `http://localhost:3000/api/debug/sessions?thread_id=${testThreadId}`
      );
      
      expect(response.ok).toBe(true);
      
      const data = await response.json();
      expect(data.type).toBe('session_diagnostics');
      expect(data.data.thread_id).toBe(testThreadId);
      expect(data.data.session_exists).toBe(true);
      
      console.log('诊断API测试通过');
    });
  });

  describe('性能和边界测试', () => {
    test('大量消息的性能测试', async () => {
      const startTime = Date.now();
      const messages = await getSessionMessages(testThreadId, 100);
      const endTime = Date.now();
      
      expect(endTime - startTime).toBeLessThan(5000); // 5秒内完成
      expect(Array.isArray(messages)).toBe(true);
      
      console.log(`大量消息查询性能: ${endTime - startTime}ms, 消息数: ${messages.length}`);
    });

    test('不存在的会话处理', async () => {
      const fakeThreadId = 'non-existent-thread-id';
      
      const currentState = await getCurrentSessionState(fakeThreadId);
      expect(currentState).toBeNull();
      
      const messages = await getSessionMessages(fakeThreadId);
      expect(messages).toEqual([]);
      
      const diagnostics = await diagnoseSession(fakeThreadId);
      expect(diagnostics.session_exists).toBe(false);
      expect(diagnostics.checkpoint_exists).toBe(false);
      
      console.log('不存在会话处理测试通过');
    });

    test('限制参数测试', async () => {
      // 测试消息限制
      const limitedMessages = await getSessionMessages(testThreadId, 1);
      expect(limitedMessages.length).toBeLessThanOrEqual(1);
      
      // 测试历史限制
      const limitedHistory = await getSessionHistory(testThreadId, 1);
      expect(limitedHistory.length).toBeLessThanOrEqual(1);
      
      console.log('限制参数测试通过');
    });
  });
});

// 手动测试函数（用于开发调试）
export async function manualTest() {
  console.log('开始手动测试...');
  
  const testThreadId = 'manual-test-' + Date.now();
  
  try {
    // 1. 创建测试会话
    console.log('1. 创建测试会话...');
    const graph = await getSimpleChatGraph();
    const config = { configurable: { thread_id: testThreadId } };
    
    await graph.invoke({ input: "你好，这是手动测试" }, config);
    
    // 2. 测试各种查询功能
    console.log('2. 测试状态查询...');
    const currentState = await getCurrentSessionState(testThreadId);
    console.log('当前状态:', currentState ? '✓' : '✗');
    
    console.log('3. 测试消息查询...');
    const messages = await getSessionMessages(testThreadId);
    console.log('消息数量:', messages.length);
    
    console.log('4. 测试诊断功能...');
    const diagnostics = await diagnoseSession(testThreadId);
    console.log('诊断结果:', diagnostics.recommendations);
    
    console.log('手动测试完成 ✓');
    
  } catch (error) {
    console.error('手动测试失败:', error);
  }
}

// 如果直接运行此文件，执行手动测试
if (require.main === module) {
  manualTest().catch(console.error);
}
