/**
 * MongoDB持久化功能测试
 * 
 * 运行测试：
 * npm test -- persistence.test.ts
 * 
 * 或者手动测试：
 * node -r ts-node/register src/tests/persistence.test.ts
 */

import { getMongoCheckpointer, checkMongoHealth, closeMongoConnection } from '../config/mongodb';
import { 
  createSession, 
  validateSession, 
  getSessionHistory, 
  deleteSession,
  buildGraphConfig 
} from '../utils/session-manager';
import { getSimpleChatGraph } from '../graph/simpleChat';
import { getDesignToCodeGraph } from '../graph/designToCode';

// 测试配置
const TEST_CONFIG = {
  timeout: 30000, // 30秒超时
  testMessages: [
    "你好，我是测试用户",
    "请告诉我今天的天气如何？",
    "谢谢你的回答"
  ]
};

/**
 * 基础连接测试
 */
async function testMongoConnection() {
  console.log('\n=== 测试MongoDB连接 ===');
  
  try {
    const isHealthy = await checkMongoHealth();
    console.log('✅ MongoDB连接状态:', isHealthy ? '正常' : '异常');
    
    if (!isHealthy) {
      throw new Error('MongoDB连接失败');
    }
    
    const checkpointer = await getMongoCheckpointer();
    console.log('✅ Checkpointer创建成功');
    
    return true;
  } catch (error) {
    console.error('❌ MongoDB连接测试失败:', error);
    return false;
  }
}

/**
 * 会话管理测试
 */
async function testSessionManagement() {
  console.log('\n=== 测试会话管理 ===');
  
  try {
    // 创建新会话
    const session = createSession('chat');
    console.log('✅ 创建会话:', session.thread_id);
    
    // 验证会话（新会话可能不存在于数据库中，直到有数据写入）
    console.log('ℹ️  新会话验证跳过（需要有数据写入后才能验证）');
    
    // 构建配置
    const config = buildGraphConfig(session.thread_id);
    console.log('✅ 构建配置成功:', config);
    
    return session.thread_id;
  } catch (error) {
    console.error('❌ 会话管理测试失败:', error);
    throw error;
  }
}

/**
 * SimpleChat持久化测试
 */
async function testSimpleChatPersistence(thread_id: string) {
  console.log('\n=== 测试SimpleChat持久化 ===');
  
  try {
    const graph = await getSimpleChatGraph();
    console.log('✅ 获取SimpleChat图成功');
    
    const config = buildGraphConfig(thread_id);
    
    // 发送测试消息
    for (let i = 0; i < TEST_CONFIG.testMessages.length; i++) {
      const message = TEST_CONFIG.testMessages[i];
      console.log(`📤 发送消息 ${i + 1}: ${message}`);
      
      const input = {
        messages: [],
        input: message,
        output: '',
        modelConfig: { type: 'google' as const }
      };
      
      try {
        const result = await graph.invoke(input, config);
        console.log(`✅ 消息 ${i + 1} 处理成功`);
        console.log(`📥 回复: ${result.output?.substring(0, 100)}...`);
      } catch (error) {
        console.error(`❌ 消息 ${i + 1} 处理失败:`, error);
        // 继续测试其他消息
      }
      
      // 短暂延迟
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // 验证会话现在应该存在
    const isValid = await validateSession(thread_id);
    console.log('✅ 会话验证:', isValid ? '存在' : '不存在');
    
    // 获取会话历史
    const history = await getSessionHistory(thread_id, 10);
    console.log('✅ 会话历史条数:', history.length);
    
    return true;
  } catch (error) {
    console.error('❌ SimpleChat持久化测试失败:', error);
    return false;
  }
}

/**
 * DesignToCode持久化测试
 */
async function testDesignToCodePersistence() {
  console.log('\n=== 测试DesignToCode持久化 ===');
  
  try {
    const session = createSession('coding');
    const graph = await getDesignToCodeGraph();
    console.log('✅ 获取DesignToCode图成功');
    
    const config = buildGraphConfig(session.thread_id);
    
    // 创建测试设计项
    const testDesignItems = [
      {
        name: "test.html",
        content: "<html><body><h1>测试页面</h1></body></html>",
        type: "html" as const
      }
    ];
    
    const input = {
      messages: [],
      input: testDesignItems,
      output: ''
    };
    
    console.log('📤 发送设计项目测试');
    
    try {
      const result = await graph.invoke(input, config);
      console.log('✅ DesignToCode处理成功');
      console.log(`📥 输出: ${result.output?.substring(0, 100)}...`);
    } catch (error) {
      console.error('❌ DesignToCode处理失败:', error);
    }
    
    // 验证会话
    const isValid = await validateSession(session.thread_id);
    console.log('✅ 编程会话验证:', isValid ? '存在' : '不存在');
    
    return session.thread_id;
  } catch (error) {
    console.error('❌ DesignToCode持久化测试失败:', error);
    return null;
  }
}

/**
 * 会话清理测试
 */
async function testSessionCleanup(thread_ids: string[]) {
  console.log('\n=== 测试会话清理 ===');
  
  try {
    for (const thread_id of thread_ids) {
      if (thread_id) {
        const success = await deleteSession(thread_id);
        console.log(`${success ? '✅' : '❌'} 删除会话 ${thread_id}: ${success ? '成功' : '失败'}`);
      }
    }
    
    return true;
  } catch (error) {
    console.error('❌ 会话清理测试失败:', error);
    return false;
  }
}

/**
 * 主测试函数
 */
async function runPersistenceTests() {
  console.log('🚀 开始MongoDB持久化功能测试...');
  
  const testResults = {
    connection: false,
    sessionManagement: false,
    simpleChatPersistence: false,
    designToCodePersistence: false,
    cleanup: false
  };
  
  const sessionIds: string[] = [];
  
  try {
    // 1. 测试MongoDB连接
    testResults.connection = await testMongoConnection();
    if (!testResults.connection) {
      throw new Error('MongoDB连接失败，停止测试');
    }
    
    // 2. 测试会话管理
    const chatThreadId = await testSessionManagement();
    testResults.sessionManagement = true;
    sessionIds.push(chatThreadId);
    
    // 3. 测试SimpleChat持久化
    testResults.simpleChatPersistence = await testSimpleChatPersistence(chatThreadId);
    
    // 4. 测试DesignToCode持久化
    const codingThreadId = await testDesignToCodePersistence();
    testResults.designToCodePersistence = !!codingThreadId;
    if (codingThreadId) {
      sessionIds.push(codingThreadId);
    }
    
    // 5. 测试会话清理
    testResults.cleanup = await testSessionCleanup(sessionIds);
    
  } catch (error) {
    console.error('💥 测试过程中发生错误:', error);
  } finally {
    // 关闭连接
    await closeMongoConnection();
  }
  
  // 输出测试结果
  console.log('\n📊 测试结果汇总:');
  console.log('================');
  Object.entries(testResults).forEach(([test, result]) => {
    console.log(`${result ? '✅' : '❌'} ${test}: ${result ? '通过' : '失败'}`);
  });
  
  const passedTests = Object.values(testResults).filter(Boolean).length;
  const totalTests = Object.keys(testResults).length;
  
  console.log(`\n🎯 总体结果: ${passedTests}/${totalTests} 测试通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！MongoDB持久化功能正常工作。');
  } else {
    console.log('⚠️  部分测试失败，请检查配置和连接。');
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runPersistenceTests().catch(console.error);
}

export { runPersistenceTests };
