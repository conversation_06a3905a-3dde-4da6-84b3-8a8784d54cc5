# Web2 版本更新记录

## 更新概述
- 日期：2024-12-18
- 版本：Web2 新版本
- 状态：已完成

## 主要变更

### 1. 技术栈升级
- 从 Express.js 后端迁移到 Next.js 全栈应用
- 使用 Vercel AI SDK 替代原生 LangChain 调用
- 采用 React 19 + TypeScript 前端架构
- 集成 Tailwind CSS + Radix UI 组件库

### 2. 功能增强
- 新增设计稿转代码功能 (DesignToCode)
- 实现简单聊天功能 (SimpleChat)
- 支持多线程并行处理
- 添加文件写入工具集成

### 3. 架构优化
- 基于 LangGraph 的工作流引擎
- 模块化的节点设计
- 状态管理和错误处理机制
- 响应式聊天界面

### 4. 开发体验
- 热重载开发环境
- TypeScript 类型安全
- ESLint + Prettier 代码规范
- 组件化开发模式

## 目录结构
```
web2/
├── src/
│   ├── app/           # Next.js App Router
│   ├── components/    # React 组件
│   ├── graph/         # LangGraph 工作流
│   │   ├── designToCode/  # 设计转代码
│   │   └── simpleChat/    # 简单聊天
│   ├── tools/         # 工具函数
│   └── utils/         # 工具函数
├── files/             # 静态文件
└── package.json       # 依赖配置
```

## 迁移说明
- 原版本保留在根目录
- 新版本位于 web2/ 目录
- 建议使用新版本进行开发
