# AI编程助手功能说明

## 新增功能

web2 项目新增了一个专门的AI编程助手页面 (`/ai-coding`)，提供强大的编程支持和文件上传功能。

## 功能特性

### 🤖 AI编程助手
- **专业编程支持**: 基于 `designToCode` agent，专门优化用于编程任务
- **多语言支持**: Python, JavaScript, TypeScript, Java, C++, Go 等
- **代码分析**: 智能分析代码质量、性能和安全性
- **优化建议**: 提供代码改进和最佳实践建议

### 📁 文件上传功能
- **拖拽上传**: 支持拖拽文件到上传区域
- **多文件支持**: 可同时上传多个文件进行分析
- **文件类型支持**:
  - 代码文件: `.js`, `.ts`, `.jsx`, `.tsx`, `.py`, `.java`, `.cpp`, `.c`, `.h`
  - 网页文件: `.html`, `.css`, `.json`, `.xml`
  - 文档文件: `.md`, `.txt`
  - 配置文件: `.yaml`, `.yml`, `.sql`, `.sh`

### 🛠️ 工具集成
- **文件生成**: 使用 `writeFileTool` 生成优化后的代码文件
- **项目结构**: 支持创建完整的项目结构
- **代码重构**: 自动重构和优化代码

## 使用方法

### 1. 访问AI编程助手
- 打开 `http://localhost:3001/ai-coding`
- 或从主页导航栏点击 "🤖 AI编程" 链接

### 2. 文件上传方式
**方式一: 拖拽上传**
1. 将代码文件拖拽到上传区域
2. 文件会自动添加到上传列表
3. 点击发送按钮开始分析

**方式二: 点击选择**
1. 点击 "选择文件或拖拽到此处" 按钮
2. 在文件选择器中选择文件
3. 文件会自动添加到上传列表

### 3. 编程咨询
- 直接在输入框中描述编程问题
- 可以结合上传的文件进行具体分析
- 支持代码解释、调试、优化等需求

## 示例用法

### 代码分析示例
1. 上传一个 JavaScript 文件
2. 输入: "请分析这个代码的性能问题并提供优化建议"
3. AI会分析代码并提供详细的优化方案

### 代码生成示例
1. 输入: "请帮我创建一个 React 组件，用于显示用户列表"
2. AI会生成完整的组件代码并可以保存到文件

### 调试帮助示例
1. 上传有问题的代码文件
2. 输入: "这个代码有什么问题？"
3. AI会识别潜在的bug并提供修复建议

## 技术实现

### 前端组件
- `CodingChatWindow`: 支持文件上传的聊天界面
- `FileUploadArea`: 文件上传和管理组件
- 基于现有的 `ChatWindow` 扩展而来

### API路由
- `/api/ai-coding`: 处理编程相关请求的API端点
- 支持文件上传和 `DesignItem[]` 格式转换
- 集成 `designToCode` agent

### Agent集成
- 使用现有的 `designToCode` agent
- 支持工具调用 (`writeFileTool`)
- 优化的系统提示词，专门用于编程任务

## 导航功能

在页面顶部添加了导航栏，包含：
- 💬 聊天: 链接到主聊天页面 (`/`)
- 🤖 AI编程: 链接到AI编程助手页面 (`/ai-coding`)

## 文件结构

```
web2/src/
├── app/
│   ├── ai-coding/
│   │   └── page.tsx              # AI编程页面
│   └── api/
│       └── ai-coding/
│           └── route.ts          # AI编程API路由
├── components/
│   ├── CodingChatWindow.tsx      # 编程聊天窗口组件
│   └── Navbar.tsx               # 导航栏组件（已更新）
└── graph/
    └── designToCode/            # 使用现有的designToCode agent
```

## 注意事项

1. **文件大小限制**: 建议上传的文件不要过大，以确保良好的性能
2. **文件格式**: 目前主要支持文本格式的代码文件
3. **安全性**: 上传的文件内容会被发送到AI模型进行分析
4. **服务依赖**: 需要后端 LangGraph 服务正常运行

## 未来扩展

- 支持更多文件格式
- 添加代码高亮显示
- 集成代码编辑器
- 支持项目级别的分析
- 添加代码版本对比功能
