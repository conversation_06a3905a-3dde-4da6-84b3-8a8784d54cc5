# LangGraph MongoDB 持久化存储指南

本指南介绍如何使用新实现的MongoDB持久化存储功能，实现chat和designToCode agent的会话历史持久化。

## 功能概述

- ✅ **会话持久化**: 使用MongoDB存储会话状态和历史
- ✅ **自动会话管理**: 自动创建和管理thread_id
- ✅ **会话恢复**: 支持从任意检查点恢复会话
- ✅ **双模式支持**: chat和coding两种会话类型
- ✅ **错误回退**: MongoDB连接失败时自动回退到内存模式

## 环境配置

### 1. 环境变量设置

在 `.env.local` 文件中添加MongoDB配置：

```bash
MONGODB_URI=****************************************************
MONGODB_DB_NAME=haicode_cli
```

### 2. 依赖包

确保已安装必要的依赖：

```bash
npm install @langchain/langgraph-checkpoint-mongodb mongodb uuid
npm install --save-dev @types/uuid
```

## API使用方法

### Chat API (`/api/chat`)

#### 创建新会话
```javascript
const response = await fetch('/api/chat', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    messages: [{ role: 'user', content: '你好' }],
    // thread_id 会自动生成
  })
});
```

#### 继续现有会话
```javascript
const response = await fetch('/api/chat', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    messages: [{ role: 'user', content: '继续我们的对话' }],
    thread_id: 'existing-thread-id-here'
  })
});
```

### AI Coding API (`/api/ai-coding`)

#### 创建新编程会话
```javascript
const response = await fetch('/api/ai-coding', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    messages: [{ role: 'user', content: '帮我创建一个网页' }],
    files: [/* 上传的文件 */],
    // thread_id 会自动生成
  })
});
```

#### 继续现有编程会话
```javascript
const response = await fetch('/api/ai-coding', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    messages: [{ role: 'user', content: '修改刚才的代码' }],
    thread_id: 'existing-coding-thread-id'
  })
});
```

### 会话管理API (`/api/sessions`)

#### 健康检查
```javascript
const health = await fetch('/api/sessions?action=health');
const result = await health.json();
// { status: 'healthy', mongodb: true }
```

#### 创建新会话
```javascript
const response = await fetch('/api/sessions', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    sessionType: 'chat' // 或 'coding'
  })
});
const { thread_id } = await response.json();
```

#### 获取会话信息
```javascript
const response = await fetch(`/api/sessions?thread_id=${thread_id}&limit=10`);
const sessionInfo = await response.json();
// { thread_id, valid: true, history: [...], historyCount: 5 }
```

#### 删除会话
```javascript
const response = await fetch(`/api/sessions?thread_id=${thread_id}`, {
  method: 'DELETE'
});
```

#### 清理过期会话
```javascript
const response = await fetch('/api/sessions?action=cleanup&maxAge=24');
const result = await response.json();
// { message: "清理了 3 个过期会话", cleanedCount: 3 }
```

## 前端集成示例

### React Hook 示例

```typescript
import { useState, useCallback } from 'react';

export function usePersistedChat() {
  const [threadId, setThreadId] = useState<string | null>(null);
  const [messages, setMessages] = useState([]);

  const sendMessage = useCallback(async (content: string) => {
    const response = await fetch('/api/chat', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        messages: [...messages, { role: 'user', content }],
        thread_id: threadId // 如果为null，会自动创建新会话
      })
    });

    // 从响应头或响应体中获取thread_id
    // 实际实现需要根据API响应格式调整
    
    return response;
  }, [messages, threadId]);

  const loadSession = useCallback(async (sessionId: string) => {
    const response = await fetch(`/api/sessions?thread_id=${sessionId}`);
    const sessionInfo = await response.json();
    
    if (sessionInfo.valid) {
      setThreadId(sessionId);
      // 根据历史重建消息列表
    }
  }, []);

  return { threadId, messages, sendMessage, loadSession };
}
```

## 测试

### 运行持久化测试

```bash
# 确保MongoDB服务正在运行
# 然后运行测试
npx ts-node src/tests/persistence.test.ts
```

### 手动测试步骤

1. **启动应用**
   ```bash
   npm run dev
   ```

2. **测试chat持久化**
   - 发送消息到 `/api/chat`
   - 记录返回的thread_id
   - 使用相同thread_id继续对话
   - 验证上下文是否保持

3. **测试coding持久化**
   - 发送编程请求到 `/api/ai-coding`
   - 记录thread_id
   - 继续相同的编程任务
   - 验证项目状态是否保持

4. **测试会话管理**
   - 使用 `/api/sessions` 查看会话信息
   - 测试会话删除功能

## 故障排除

### 常见问题

1. **MongoDB连接失败**
   - 检查MongoDB服务是否运行
   - 验证连接字符串和认证信息
   - 查看控制台日志

2. **会话不持久化**
   - 检查thread_id是否正确传递
   - 验证MongoDB写入权限
   - 查看API响应中的错误信息

3. **性能问题**
   - 考虑添加MongoDB索引
   - 实施会话清理策略
   - 监控数据库连接数

### 日志监控

应用会输出详细的日志信息：

```
MongoDB: 连接成功
SimpleChat: 图已创建并编译完成（带持久化支持）
SessionManager: 创建新会话 abc-123-def (类型: chat)
Chat API: 使用会话ID abc-123-def
```

## 最佳实践

1. **会话生命周期管理**
   - 定期清理过期会话
   - 为长期会话设置合理的TTL
   - 监控数据库存储使用情况

2. **错误处理**
   - 始终处理MongoDB连接失败的情况
   - 提供用户友好的错误信息
   - 实施重试机制

3. **安全考虑**
   - 验证thread_id的有效性
   - 实施适当的访问控制
   - 定期备份重要会话数据

## 扩展功能

未来可以考虑添加的功能：

- 会话分享和协作
- 会话导出和导入
- 高级搜索和过滤
- 会话分析和统计
- 多租户支持
