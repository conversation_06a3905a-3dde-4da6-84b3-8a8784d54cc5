# HT LangGraph MVP - Web2 版本

基于 Next.js 和 LangGraph 的现代化 AI 应用开发框架，提供设计稿转代码、智能聊天等核心功能。

## 🚀 新版本特性

### 🌟 核心功能
- **设计稿转代码**: 基于 LangGraph 的智能设计稿解析和代码生成
- **智能聊天**: 支持多轮对话的 AI 聊天系统(测试)
- **并行处理**: 多线程并行执行，提高处理效率
- **文件工具**: 集成文件写入和管理功能
- **响应式界面**: 现代化的 React 19 + Tailwind CSS 界面

### 🏗️ 技术架构
- **Next.js 15**: 全栈 React 框架，支持 SSR/SSG
- **Vercel AI SDK**: 优化的 AI 模型交互
- **LangGraph**: 复杂工作流和智能体编排
- **TypeScript**: 完整的类型安全
- **Tailwind CSS**: 现代化样式系统

## 📁 项目结构

```
web2/                         # 🆕 新版本目录
├── src/
│   ├── app/                  # Next.js App Router
│   │   ├── api/              # API 路由
│   │   ├── ai-coding/        # AI 编程页面
│   │   └── page.tsx          # 主页
│   ├── components/           # React 组件库
│   ├── graph/                # LangGraph 工作流
│   │   ├── designToCode/     # 设计转代码引擎
│   │   │   ├── nodes/        # 工作流节点
│   │   │   ├── model/        # 数据模型
│   │   │   └── utils.ts      # 工具函数
│   │   └── simpleChat/       # 简单聊天引擎
│   ├── tools/                # 工具函数
│   └── utils/                # 通用工具
├── files/                    # 静态文件
└── package.json              # 项目配置
```

## 🚀 快速开始

### 环境要求
- Node.js >= 18.0.0
- npm 或 pnpm

### 安装和运行

1. **进入新版本目录**
```bash
cd web2
```

2. **安装依赖**
```bash
npm install
```

3. **启动开发服务器**
```bash
npm run dev
```

4. **访问应用**
打开 [http://localhost:3000](http://localhost:3000) 查看结果

## 🎯 核心功能详解

### 设计稿转代码 (DesignToCode)

基于 LangGraph 的智能设计稿解析系统，支持：

- **并行处理**: 多个设计稿同时处理
- **HTML 生成**: 自动生成响应式 HTML 代码
- **代码合并**: 智能合并多个页面代码
- **项目生成**: 生成完整的项目结构

#### 工作流程图

```mermaid
graph TD
    START([输入设计稿]) --> PARALLEL[并行处理]
    PARALLEL --> HTML[生成HTML]
    HTML --> COMBINE[合并代码]
    COMBINE --> PROJECT[生成项目]
    PROJECT --> END([输出结果])
```

### 智能聊天 (SimpleChat)

支持多轮对话的 AI 聊天系统：

- **上下文记忆**: 保持对话历史
- **流式响应**: 实时显示 AI 回复
- **Markdown 渲染**: 支持代码块和表格
- **响应式设计**: 适配各种设备

## 🛠️ 开发指南

### 添加新的工作流节点

1. 在 `src/graph/` 下创建新的工作流目录
2. 实现节点逻辑：
```typescript
import { StateGraph } from "@langchain/langgraph";

export const graph = new StateGraph({
  channels: {
    messages: {
      value: (x: BaseMessage[], y: BaseMessage[]) => x.concat(y),
      default: () => [],
    },
    // 其他状态字段...
  },
});
```

3. 在 `src/app/api/` 下添加对应的 API 路由

### 自定义组件

使用 Radix UI + Tailwind CSS 创建组件：

```typescript
import { Button } from "@/components/ui/button";

export function CustomComponent() {
  return (
    <Button variant="outline" className="w-full">
      自定义按钮
    </Button>
  );
}
```

## 📊 API 文档

### 设计稿转代码 API

**POST** `/api/ai-coding`

请求体：
```json
{
  "designItems": [
    {
      "id": "page1",
      "content": "设计稿内容...",
      "type": "html"
    }
  ]
}
```

### 聊天 API

**POST** `/api/chat`

请求体：
```json
{
  "messages": [
    {
      "role": "user",
      "content": "你好"
    }
  ]
}
```

### 代码检查
```bash
npm run lint
npm run format
```

## 🔧 配置

### 开发配置
- `next.config.js`: Next.js 配置
- `tailwind.config.js`: Tailwind CSS 配置
- `tsconfig.json`: TypeScript 配置

## 📈 性能优化

### 生产构建
```bash
npm run build
npm start
```

## 📝 版本说明

- **原版本**: 位于项目根目录，基于 langgraph-api
- **新版本**: 位于 `web2/` 目录，基于 Next.js + LangGraph
