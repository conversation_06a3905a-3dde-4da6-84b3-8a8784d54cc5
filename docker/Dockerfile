# FROM 168.61.124.82/public/node:16.15.1 as builder
FROM repo-dev.htsc/public-cncp-image-base-local/node:20 as builder
RUN npm config set registry http://registry.npm.htsc/
COPY web2/package.json /app/web2/
COPY web2/yarn.lock /app/web2/
COPY web2/.npmrc /app/web2/.npmrc

WORKDIR /app/web2

RUN yarn install

ARG EMAS_BUILD_ID

# COPY .  /app/
COPY web2/src /app/web2/src
COPY web2/.eslintrc.json /app/web2
COPY web2/.prettierrc.json /app/web2
COPY web2/components.json /app/web2
COPY web2/next.config.js /app/web2
COPY web2/postcss.config.js /app/web2
COPY web2/tailwind.config.js /app/web2
COPY web2/tsconfig.json /app/web2

RUN yarn build

FROM repo-dev.htsc/public-cncp-image-base-local/node:20 as deploy

COPY static /app/static
COPY docker /app/docker
COPY --from=builder /app/web2 /app/web2

WORKDIR /app/web2

RUN  set -x \
    && chmod +x /app/docker/start.sh

ENTRYPOINT ["/app/docker/start.sh"]
